<?php
/**
 * Test Job Creation Script
 * 
 * This script tests the job creation functionality to debug the blank page issue.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load configuration
require_once 'config.php';

// Load required files
require_once 'includes/Database.php';
require_once 'includes/JobManager.php';

echo "<h1>Job Creation Test</h1>";

try {
    // Initialize database connection
    echo "<p>Initializing database connection...</p>";
    $db = new Database();
    echo "<p>✓ Database connection successful</p>";

    // Initialize JobManager
    echo "<p>Initializing JobManager...</p>";
    $jobManager = new JobManager($db);
    echo "<p>✓ JobManager initialized</p>";

    // Test job data
    $jobData = [
        'name' => 'Test Job ' . date('Y-m-d H:i:s'),
        'url' => 'https://example.com',
        'type' => 'wordpress',
        'posts_per_run' => 10,
        'category' => null,
        'after_date' => null,
        'before_date' => null,
        'schedule_type' => null,
        'disable_embed' => 0
    ];

    echo "<p>Test job data:</p>";
    echo "<pre>" . json_encode($jobData, JSON_PRETTY_PRINT) . "</pre>";

    // Try to create the job
    echo "<p>Creating job...</p>";
    $jobId = $jobManager->createJob($jobData);
    echo "<p>✓ Job created successfully with ID: $jobId</p>";

    // Verify the job was created
    echo "<p>Verifying job creation...</p>";
    $job = $jobManager->getJob($jobId);
    
    if ($job) {
        echo "<p>✓ Job verification successful</p>";
        echo "<p>Job details:</p>";
        echo "<pre>" . json_encode($job, JSON_PRETTY_PRINT) . "</pre>";
    } else {
        echo "<p>✗ Job verification failed - job not found</p>";
    }

    // List all jobs
    echo "<p>All jobs in database:</p>";
    $jobs = $jobManager->getJobs();
    if (empty($jobs)) {
        echo "<p>No jobs found</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>URL</th><th>Type</th><th>Status</th><th>Created</th></tr>";
        foreach ($jobs as $job) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($job['id']) . "</td>";
            echo "<td>" . htmlspecialchars($job['name']) . "</td>";
            echo "<td>" . htmlspecialchars($job['url']) . "</td>";
            echo "<td>" . htmlspecialchars($job['type']) . "</td>";
            echo "<td>" . htmlspecialchars($job['status']) . "</td>";
            echo "<td>" . htmlspecialchars($job['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<p><a href='" . BASE_URL . "/?page=jobs'>Back to Jobs</a></p>";
?>
