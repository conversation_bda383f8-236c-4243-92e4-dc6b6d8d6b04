<?php
/**
 * Debug Form Submission Script
 * 
 * This script helps debug form submission issues.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load configuration
require_once 'config.php';

echo "<h1>Form Submission Debug</h1>";

echo "<h2>Request Information</h2>";
echo "<p><strong>Request Method:</strong> " . $_SERVER['REQUEST_METHOD'] . "</p>";
echo "<p><strong>Request URI:</strong> " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p><strong>Query String:</strong> " . ($_SERVER['QUERY_STRING'] ?? 'None') . "</p>";

echo "<h2>GET Parameters</h2>";
if (empty($_GET)) {
    echo "<p>No GET parameters</p>";
} else {
    echo "<pre>" . json_encode($_GET, JSON_PRETTY_PRINT) . "</pre>";
}

echo "<h2>POST Parameters</h2>";
if (empty($_POST)) {
    echo "<p>No POST parameters</p>";
} else {
    echo "<pre>" . json_encode($_POST, JSON_PRETTY_PRINT) . "</pre>";
}

echo "<h2>Session Information</h2>";
session_start();
if (empty($_SESSION)) {
    echo "<p>No session data</p>";
} else {
    echo "<pre>" . json_encode($_SESSION, JSON_PRETTY_PRINT) . "</pre>";
}

echo "<h2>Headers</h2>";
$headers = getallheaders();
if (empty($headers)) {
    echo "<p>No headers available</p>";
} else {
    echo "<pre>" . json_encode($headers, JSON_PRETTY_PRINT) . "</pre>";
}

// Test form
echo "<h2>Test Form</h2>";
echo '<form method="post" action="debug_form_submission.php?page=jobs&action=new">';
echo '<p><label>Name: <input type="text" name="name" value="Test Job" required></label></p>';
echo '<p><label>URL: <input type="url" name="url" value="https://example.com" required></label></p>';
echo '<p><label>Type: <select name="type"><option value="wordpress">WordPress</option><option value="sitemap">Sitemap</option></select></label></p>';
echo '<p><label>Posts per run: <input type="number" name="posts_per_run" value="10" min="1" max="100"></label></p>';
echo '<p><input type="submit" value="Test Submit"></p>';
echo '</form>';

// If this is a POST request, try to process it
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>Processing Form Submission</h2>";
    
    try {
        // Load required files
        require_once 'includes/Database.php';
        require_once 'includes/JobManager.php';

        // Initialize database connection
        echo "<p>Initializing database...</p>";
        $db = new Database();
        echo "<p>✓ Database initialized</p>";

        // Initialize JobManager
        echo "<p>Initializing JobManager...</p>";
        $jobManager = new JobManager($db);
        echo "<p>✓ JobManager initialized</p>";

        // Prepare job data
        $jobData = [
            'name' => $_POST['name'] ?? '',
            'url' => $_POST['url'] ?? '',
            'type' => $_POST['type'] ?? 'wordpress',
            'posts_per_run' => (int)($_POST['posts_per_run'] ?? 10),
            'category' => $_POST['category'] ?? null,
            'after_date' => $_POST['after_date'] ?? null,
            'before_date' => $_POST['before_date'] ?? null,
            'schedule_type' => $_POST['schedule_type'] ?? null,
            'disable_embed' => isset($_POST['disable_embed']) ? 1 : 0
        ];

        echo "<p>Job data prepared:</p>";
        echo "<pre>" . json_encode($jobData, JSON_PRETTY_PRINT) . "</pre>";

        // Create job
        echo "<p>Creating job...</p>";
        $jobId = $jobManager->createJob($jobData);
        echo "<p>✓ Job created successfully with ID: $jobId</p>";

        // Test redirect (but don't actually redirect)
        $redirectUrl = BASE_URL . '/?page=jobs&success=' . urlencode('Job created successfully!');
        echo "<p>Would redirect to: <a href='$redirectUrl'>$redirectUrl</a></p>";

    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p>Stack trace:</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    }
}

echo "<p><a href='" . BASE_URL . "/?page=jobs'>Back to Jobs</a></p>";
?>
