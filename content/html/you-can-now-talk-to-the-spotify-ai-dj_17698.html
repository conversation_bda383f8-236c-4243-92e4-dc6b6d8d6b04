<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>You Can Now Talk to the Spotify AI DJ</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        img {
            max-width: 100%;
            height: auto;
        }
        .meta {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>You Can Now Talk to the Spotify AI DJ</h1>
    <div class="meta">Published on: May 13, 2025</div>
    <div class="content"><!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "http://www.w3.org/TR/REC-html40/loose.dtd">
<html><body><p><a href="https://newsroom.spotify.com/2023-02-22/spotify-debuts-a-new-ai-dj-right-in-your-pocket/" target="_blank" rel="noreferrer noopener">Spotify</a> is releasing a new feature that gives its AI DJ a much-needed upgrade: voice control. Starting May 13, 2025, Premium users in English-speaking regions can now press and hold the DJ button. Then they tell it what they want to hear.</p><p>That means users do not have to settle for whatever the AI DJ picks next.&nbsp;</p><h2 class="wp-block-heading"><strong>How To Use Voice Commands With Spotify&rsquo;s AI DJ</strong></h2><p>Here&rsquo;s how it works:</p><ul class="wp-block-list"><li><strong>Open the AI DJ feature</strong> on the Spotify mobile app.</li>

<li><strong>Tap and hold</strong> the DJ button in the bottom-right corner of your screen.</li>

<li><strong>Listen for the beep</strong>, that means the AI is ready.</li>

<li><strong>Say your request out loud</strong>, like:<br><ul class="wp-block-list"><li>&ldquo;Play upbeat pop for a workout.&rdquo;</li>

<li>&ldquo;Give me mellow jazz for dinner.&rdquo;</li>

<li>&ldquo;Surprise me with indie tracks I haven&rsquo;t heard.&rdquo;</li></ul></li></ul><p>The DJ will then use your request to curate what comes next.</p><p><strong>Note:</strong> You&rsquo;ll need a Spotify Premium account and the app updated to the latest version to use this.</p><h2 class="wp-block-heading"><strong>What&rsquo;s New?</strong></h2><figure class="wp-block-image"><img decoding="async" src="http://localhost/sc/content/images/you-can-now-talk-to-the-spotify-ai-dj_17698/8b9cd8bdca30326c0294333afaf7c0db.jpg" alt="The Spotify logo"></figure><p>Before this update, the AI DJ worked a bit like a radio station. It gave you music based on your taste, but you couldn&rsquo;t really change the direction, only skip songs.</p><p>Now, Spotify is putting the user in the driver&rsquo;s seat. Although the DJ still uses your listening history, now it&rsquo;s guided by your voice.</p><h2 class="wp-block-heading"><strong>A Musical Sidekick</strong></h2><p>Spotify is blending this voice control with features from its AI Playlist beta, which let users type fun prompts like:</p><ul class="wp-block-list"><li>&ldquo;Music for cooking like I&rsquo;m on a cooking show.&rdquo;</li>

<li>&ldquo;Songs that feel like a road trip through the desert.&rdquo;</li></ul><p>Now, you can say things like:</p><ul class="wp-block-list"><li>&ldquo;Play something that sounds like a movie soundtrack.&rdquo;</li>

<li>&ldquo;I want futuristic beats for coding.&rdquo;</li></ul><p><strong>Also read:</strong> <a href="https://autogpt.net/spotify-wrapped-2024-ai-powered-podcasts-musical-evolution-and-more-surprises/" target="_blank" rel="noreferrer noopener">Spotify Wrapped 2024 Is Here and It Comes With a Twist</a></p><h2 class="wp-block-heading"><strong>Where It Might Fall Short</strong></h2><p>The update is exciting, but it&rsquo;s not perfect.</p><ul class="wp-block-list"><li><strong>You can&rsquo;t type your requests.</strong> If you&rsquo;re in a place where you can&rsquo;t talk out loud, like a bus or a classroom.</li>

<li><strong>Specific tracks?</strong> It may not queue up that one song you&rsquo;ve been thinking about. If you know exactly what you want, searching manually is still faster.</li>

<li><strong>Still only in English.</strong> Spotify hasn&rsquo;t said when, or if, voice commands will expand to other languages.</li></ul><p></p></body></html>
</div>
</body>
</html>