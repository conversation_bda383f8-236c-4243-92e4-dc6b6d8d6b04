<?php
/**
 * Grab API
 * 
 * This file handles API requests for content grabbing.
 */

// Only allow POST requests
if ($method !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => 'Method not allowed'
    ]);
    exit;
}

try {
    // Validate required fields
    $requiredFields = ['url'];
    foreach ($requiredFields as $field) {
        if (empty($data[$field])) {
            throw new Exception("Field '$field' is required");
        }
    }
    
    // Get URL and options
    $url = $data['url'];
    $options = [];
    
    // Set options if provided
    if (isset($data['category'])) {
        $options['category'] = $data['category'];
    }
    
    if (isset($data['after_date'])) {
        $options['after_date'] = $data['after_date'];
    }
    
    if (isset($data['before_date'])) {
        $options['before_date'] = $data['before_date'];
    }
    
    // Set limit
    $limit = isset($data['limit']) ? (int)$data['limit'] : 10;
    
    // Create grabber
    $grabber = ContentGrabber::create($db, $url, $options);
    
    // Grab content
    $posts = $grabber->grab($limit);
    
    // Save posts to database if requested
    $savedPosts = [];
    if (isset($data['save']) && $data['save']) {
        // Create a temporary job for these posts
        $jobData = [
            'name' => 'API Grab - ' . date('Y-m-d H:i:s'),
            'url' => $url,
            'type' => ContentGrabber::isWordPressSite($url) ? 'wordpress' : 'sitemap',
            'status' => 'completed',
            'posts_per_run' => $limit,
            'created_at' => date('Y-m-d H:i:s'),
            'last_run' => date('Y-m-d H:i:s')
        ];
        
        // Add options to job data
        if (isset($options['category'])) {
            $jobData['category'] = $options['category'];
        }
        
        if (isset($options['after_date'])) {
            $jobData['after_date'] = $options['after_date'];
        }
        
        if (isset($options['before_date'])) {
            $jobData['before_date'] = $options['before_date'];
        }
        
        // Create job
        $jobManager = new JobManager($db);
        $jobId = $jobManager->createJob($jobData);
        
        // Save posts
        $savedPosts = $jobManager->savePosts($posts, $jobId);
    }
    
    // Return response
    echo json_encode([
        'success' => true,
        'posts_count' => count($posts),
        'posts' => $posts,
        'saved_posts' => $savedPosts
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
