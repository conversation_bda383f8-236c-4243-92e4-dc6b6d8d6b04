<?php
/**
 * Jobs API
 * 
 * This file handles API requests for job management.
 */

// Initialize JobManager
$jobManager = new JobManager($db);

// Handle request based on method
switch ($method) {
    case 'GET':
        // Get job(s)
        if (isset($_GET['id'])) {
            // Get specific job
            $jobId = (int)$_GET['id'];
            $job = $jobManager->getJob($jobId);
            
            if ($job) {
                echo json_encode([
                    'success' => true,
                    'job' => $job
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'error' => 'Job not found'
                ]);
            }
        } else {
            // Get all jobs with optional filters
            $filters = [];
            
            if (isset($_GET['status'])) {
                $filters['status'] = $_GET['status'];
            }
            
            if (isset($_GET['type'])) {
                $filters['type'] = $_GET['type'];
            }
            
            $jobs = $jobManager->getJobs($filters);
            
            echo json_encode([
                'success' => true,
                'jobs' => $jobs
            ]);
        }
        break;
        
    case 'POST':
        // Create new job
        try {
            // Validate required fields
            $requiredFields = ['name', 'url', 'type', 'posts_per_run'];
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    throw new Exception("Field '$field' is required");
                }
            }
            
            // Create job
            $jobId = $jobManager->createJob($data);
            
            echo json_encode([
                'success' => true,
                'message' => 'Job created successfully',
                'job_id' => $jobId
            ]);
        } catch (Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
        break;
        
    case 'PUT':
        // Update job
        if (isset($_GET['id'])) {
            $jobId = (int)$_GET['id'];
            $job = $jobManager->getJob($jobId);
            
            if ($job) {
                try {
                    // Update job
                    $jobManager->updateJob($jobId, $data);
                    
                    echo json_encode([
                        'success' => true,
                        'message' => 'Job updated successfully'
                    ]);
                } catch (Exception $e) {
                    http_response_code(400);
                    echo json_encode([
                        'success' => false,
                        'error' => $e->getMessage()
                    ]);
                }
            } else {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'error' => 'Job not found'
                ]);
            }
        } else {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Job ID is required'
            ]);
        }
        break;
        
    case 'DELETE':
        // Delete job
        if (isset($_GET['id'])) {
            $jobId = (int)$_GET['id'];
            $job = $jobManager->getJob($jobId);
            
            if ($job) {
                // Delete job
                $jobManager->deleteJob($jobId);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Job deleted successfully'
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'error' => 'Job not found'
                ]);
            }
        } else {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Job ID is required'
            ]);
        }
        break;
        
    default:
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
        break;
}

// Special action: run job
if ($method === 'POST' && isset($_GET['action']) && $_GET['action'] === 'run' && isset($_GET['id'])) {
    $jobId = (int)$_GET['id'];
    $job = $jobManager->getJob($jobId);
    
    if ($job) {
        try {
            // Run job
            $result = $jobManager->runJob($jobId);
            
            echo json_encode([
                'success' => true,
                'message' => 'Job ran successfully',
                'result' => $result
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    } else {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'Job not found'
        ]);
    }
}
