<?php
/**
 * Posts API
 * 
 * This file handles API requests for post management.
 */

// Handle request based on method
switch ($method) {
    case 'GET':
        // Get post(s)
        if (isset($_GET['id'])) {
            // Get specific post
            $postId = (int)$_GET['id'];
            $post = $db->getRow("SELECT * FROM posts WHERE id = ?", [$postId]);
            
            if ($post) {
                // Get post categories
                $categories = $db->query("
                    SELECT c.* 
                    FROM categories c 
                    JOIN post_categories pc ON c.id = pc.category_id 
                    WHERE pc.post_id = ?
                ", [$postId]);
                
                // Get post tags
                $tags = $db->query("
                    SELECT t.* 
                    FROM tags t 
                    JOIN post_tags pt ON t.id = pt.tag_id 
                    WHERE pt.post_id = ?
                ", [$postId]);
                
                // Get post images
                $images = $db->query("SELECT * FROM images WHERE post_id = ?", [$postId]);
                
                // Add related data to post
                $post['categories'] = $categories;
                $post['tags'] = $tags;
                $post['images'] = $images;
                $post['featured_image'] = json_decode($post['featured_image'], true);
                
                echo json_encode([
                    'success' => true,
                    'post' => $post
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'error' => 'Post not found'
                ]);
            }
        } else {
            // Get all posts with optional filters
            $sql = "SELECT * FROM posts";
            $params = [];
            $where = [];
            
            // Filter by job ID
            if (isset($_GET['job_id'])) {
                $where[] = "job_id = ?";
                $params[] = (int)$_GET['job_id'];
            }
            
            // Filter by date range
            if (isset($_GET['after_date'])) {
                $where[] = "date_published >= ?";
                $params[] = $_GET['after_date'];
            }
            
            if (isset($_GET['before_date'])) {
                $where[] = "date_published <= ?";
                $params[] = $_GET['before_date'];
            }
            
            // Apply where clause
            if (!empty($where)) {
                $sql .= " WHERE " . implode(' AND ', $where);
            }
            
            // Apply order and limit
            $sql .= " ORDER BY date_published DESC";
            
            if (isset($_GET['limit'])) {
                $limit = (int)$_GET['limit'];
                $sql .= " LIMIT ?";
                $params[] = $limit;
            }
            
            $posts = $db->query($sql, $params);
            
            echo json_encode([
                'success' => true,
                'posts' => $posts
            ]);
        }
        break;
        
    case 'PUT':
        // Update post
        if (isset($_GET['id'])) {
            $postId = (int)$_GET['id'];
            $post = $db->getRow("SELECT * FROM posts WHERE id = ?", [$postId]);
            
            if ($post) {
                try {
                    // Allowed fields to update
                    $allowedFields = ['title', 'content', 'excerpt'];
                    $updateData = [];
                    
                    foreach ($allowedFields as $field) {
                        if (isset($data[$field])) {
                            $updateData[$field] = $data[$field];
                        }
                    }
                    
                    if (!empty($updateData)) {
                        // Add updated timestamp
                        $updateData['updated_at'] = date('Y-m-d H:i:s');
                        
                        // Update post
                        $db->update('posts', $updateData, 'id = ?', [$postId]);
                        
                        echo json_encode([
                            'success' => true,
                            'message' => 'Post updated successfully'
                        ]);
                    } else {
                        http_response_code(400);
                        echo json_encode([
                            'success' => false,
                            'error' => 'No valid fields to update'
                        ]);
                    }
                } catch (Exception $e) {
                    http_response_code(500);
                    echo json_encode([
                        'success' => false,
                        'error' => $e->getMessage()
                    ]);
                }
            } else {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'error' => 'Post not found'
                ]);
            }
        } else {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Post ID is required'
            ]);
        }
        break;
        
    case 'DELETE':
        // Delete post
        if (isset($_GET['id'])) {
            $postId = (int)$_GET['id'];
            $post = $db->getRow("SELECT * FROM posts WHERE id = ?", [$postId]);
            
            if ($post) {
                try {
                    // Start transaction
                    $db->beginTransaction();
                    
                    // Delete post images
                    $db->delete('images', 'post_id = ?', [$postId]);
                    
                    // Delete post category relationships
                    $db->delete('post_categories', 'post_id = ?', [$postId]);
                    
                    // Delete post tag relationships
                    $db->delete('post_tags', 'post_id = ?', [$postId]);
                    
                    // Delete post
                    $db->delete('posts', 'id = ?', [$postId]);
                    
                    // Commit transaction
                    $db->commit();
                    
                    echo json_encode([
                        'success' => true,
                        'message' => 'Post deleted successfully'
                    ]);
                } catch (Exception $e) {
                    // Rollback transaction
                    $db->rollback();
                    
                    http_response_code(500);
                    echo json_encode([
                        'success' => false,
                        'error' => $e->getMessage()
                    ]);
                }
            } else {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'error' => 'Post not found'
                ]);
            }
        } else {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => 'Post ID is required'
            ]);
        }
        break;
        
    default:
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
        break;
}

// Special actions
if ($method === 'POST') {
    if (isset($_GET['action']) && isset($_GET['id'])) {
        $postId = (int)$_GET['id'];
        $post = $db->getRow("SELECT * FROM posts WHERE id = ?", [$postId]);
        
        if (!$post) {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'error' => 'Post not found'
            ]);
            exit;
        }
        
        switch ($_GET['action']) {
            case 'regenerate_html':
                // Regenerate HTML file
                try {
                    // Implementation similar to process.php
                    // ...
                    
                    echo json_encode([
                        'success' => true,
                        'message' => 'HTML file regenerated successfully'
                    ]);
                } catch (Exception $e) {
                    http_response_code(500);
                    echo json_encode([
                        'success' => false,
                        'error' => $e->getMessage()
                    ]);
                }
                break;
                
            case 'regenerate_pdf':
                // Regenerate PDF file
                try {
                    // Implementation similar to process.php
                    // ...
                    
                    echo json_encode([
                        'success' => true,
                        'message' => 'PDF file regenerated successfully'
                    ]);
                } catch (Exception $e) {
                    http_response_code(500);
                    echo json_encode([
                        'success' => false,
                        'error' => $e->getMessage()
                    ]);
                }
                break;
                
            default:
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'error' => 'Invalid action'
                ]);
                break;
        }
    } else {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Action and post ID are required'
        ]);
    }
}
