<?php
/**
 * Analytics Class
 * 
 * This class handles analytics and reporting for the application.
 */
class Analytics {
    private $db;
    
    /**
     * Constructor
     * 
     * @param Database $db Database instance
     */
    public function __construct(Database $db) {
        $this->db = $db;
    }
    
    /**
     * Get dashboard statistics
     * 
     * @return array Statistics
     */
    public function getDashboardStats() {
        $stats = [
            'total_jobs' => $this->db->getValue("SELECT COUNT(*) FROM jobs"),
            'total_posts' => $this->db->getValue("SELECT COUNT(*) FROM posts"),
            'total_images' => $this->db->getValue("SELECT COUNT(*) FROM images"),
            'total_categories' => $this->db->getValue("SELECT COUNT(*) FROM categories"),
            'total_tags' => $this->db->getValue("SELECT COUNT(*) FROM tags"),
            'total_users' => $this->db->getValue("SELECT COUNT(*) FROM users"),
            'posts_last_24h' => $this->db->getValue("SELECT COUNT(*) FROM posts WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)"),
            'posts_last_7d' => $this->db->getValue("SELECT COUNT(*) FROM posts WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)"),
            'posts_last_30d' => $this->db->getValue("SELECT COUNT(*) FROM posts WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)"),
            'jobs_last_24h' => $this->db->getValue("SELECT COUNT(*) FROM jobs WHERE last_run >= DATE_SUB(NOW(), INTERVAL 1 DAY)"),
            'storage_usage' => $this->calculateStorageUsage(),
            'avg_images_per_post' => $this->calculateAvgImagesPerPost(),
            'top_categories' => $this->getTopCategories(5),
            'top_tags' => $this->getTopTags(5),
            'recent_activity' => $this->getRecentActivity(10)
        ];
        
        return $stats;
    }
    
    /**
     * Calculate storage usage
     * 
     * @return array Storage usage statistics
     */
    public function calculateStorageUsage() {
        $usage = [
            'images' => 0,
            'html' => 0,
            'pdf' => 0,
            'total' => 0
        ];
        
        // Calculate image storage
        $images = $this->db->query("SELECT local_path FROM images");
        foreach ($images as $image) {
            if (file_exists($image['local_path'])) {
                $size = filesize($image['local_path']);
                $usage['images'] += $size;
                $usage['total'] += $size;
            }
        }
        
        // Calculate HTML storage
        $posts = $this->db->query("SELECT html_file FROM posts WHERE html_file IS NOT NULL");
        foreach ($posts as $post) {
            if (file_exists($post['html_file'])) {
                $size = filesize($post['html_file']);
                $usage['html'] += $size;
                $usage['total'] += $size;
            }
        }
        
        // Calculate PDF storage
        $posts = $this->db->query("SELECT pdf_file FROM posts WHERE pdf_file IS NOT NULL");
        foreach ($posts as $post) {
            if (file_exists($post['pdf_file'])) {
                $size = filesize($post['pdf_file']);
                $usage['pdf'] += $size;
                $usage['total'] += $size;
            }
        }
        
        // Convert to human-readable format
        foreach ($usage as $key => $value) {
            $usage[$key . '_formatted'] = $this->formatBytes($value);
        }
        
        return $usage;
    }
    
    /**
     * Calculate average images per post
     * 
     * @return float Average number of images per post
     */
    public function calculateAvgImagesPerPost() {
        $totalPosts = $this->db->getValue("SELECT COUNT(*) FROM posts");
        $totalImages = $this->db->getValue("SELECT COUNT(*) FROM images");
        
        return $totalPosts > 0 ? round($totalImages / $totalPosts, 2) : 0;
    }
    
    /**
     * Get top categories
     * 
     * @param int $limit Maximum number of categories to return
     * @return array Top categories
     */
    public function getTopCategories($limit = 5) {
        return $this->db->query("
            SELECT c.name, COUNT(pc.post_id) as post_count
            FROM categories c
            JOIN post_categories pc ON c.id = pc.category_id
            GROUP BY c.id
            ORDER BY post_count DESC
            LIMIT ?
        ", [$limit]);
    }
    
    /**
     * Get top tags
     * 
     * @param int $limit Maximum number of tags to return
     * @return array Top tags
     */
    public function getTopTags($limit = 5) {
        return $this->db->query("
            SELECT t.name, COUNT(pt.post_id) as post_count
            FROM tags t
            JOIN post_tags pt ON t.id = pt.tag_id
            GROUP BY t.id
            ORDER BY post_count DESC
            LIMIT ?
        ", [$limit]);
    }
    
    /**
     * Get recent activity
     * 
     * @param int $limit Maximum number of activities to return
     * @return array Recent activities
     */
    public function getRecentActivity($limit = 10) {
        return $this->db->query("
            SELECT a.*, u.username
            FROM user_activity a
            JOIN users u ON a.user_id = u.id
            ORDER BY a.created_at DESC
            LIMIT ?
        ", [$limit]);
    }
    
    /**
     * Get job statistics
     * 
     * @param int|null $jobId Job ID (null for all jobs)
     * @return array Job statistics
     */
    public function getJobStats($jobId = null) {
        $stats = [];
        $params = [];
        $whereClause = '';
        
        if ($jobId !== null) {
            $whereClause = 'WHERE j.id = ?';
            $params[] = $jobId;
        }
        
        // Get job statistics
        $jobStats = $this->db->query("
            SELECT 
                j.id,
                j.name,
                j.type,
                j.status,
                j.created_at,
                j.last_run,
                COUNT(p.id) as post_count,
                MIN(p.date_published) as oldest_post,
                MAX(p.date_published) as newest_post,
                (SELECT COUNT(*) FROM images i JOIN posts p2 ON i.post_id = p2.id WHERE p2.job_id = j.id) as image_count
            FROM jobs j
            LEFT JOIN posts p ON j.id = p.job_id
            $whereClause
            GROUP BY j.id
            ORDER BY j.created_at DESC
        ", $params);
        
        foreach ($jobStats as &$job) {
            // Calculate success rate
            $totalRuns = $this->db->getValue("
                SELECT COUNT(*) FROM job_runs WHERE job_id = ?
            ", [$job['id']]);
            
            $successfulRuns = $this->db->getValue("
                SELECT COUNT(*) FROM job_runs WHERE job_id = ? AND status = 'completed'
            ", [$job['id']]);
            
            $job['total_runs'] = $totalRuns;
            $job['successful_runs'] = $successfulRuns;
            $job['success_rate'] = $totalRuns > 0 ? round(($successfulRuns / $totalRuns) * 100, 2) : 0;
            
            // Get recent runs
            $job['recent_runs'] = $this->db->query("
                SELECT * FROM job_runs
                WHERE job_id = ?
                ORDER BY start_time DESC
                LIMIT 5
            ", [$job['id']]);
        }
        
        $stats['jobs'] = $jobStats;
        
        // Get overall statistics
        $stats['total_jobs'] = count($jobStats);
        $stats['active_jobs'] = count(array_filter($jobStats, function($job) {
            return $job['status'] != 'failed';
        }));
        $stats['failed_jobs'] = count(array_filter($jobStats, function($job) {
            return $job['status'] == 'failed';
        }));
        $stats['total_posts'] = array_sum(array_column($jobStats, 'post_count'));
        $stats['total_images'] = array_sum(array_column($jobStats, 'image_count'));
        
        return $stats;
    }
    
    /**
     * Get post statistics
     * 
     * @param int|null $postId Post ID (null for all posts)
     * @return array Post statistics
     */
    public function getPostStats($postId = null) {
        $stats = [];
        $params = [];
        $whereClause = '';
        
        if ($postId !== null) {
            $whereClause = 'WHERE p.id = ?';
            $params[] = $postId;
        }
        
        // Get post statistics
        $postStats = $this->db->query("
            SELECT 
                p.id,
                p.title,
                p.date_published,
                p.created_at,
                j.name as job_name,
                (SELECT COUNT(*) FROM images WHERE post_id = p.id) as image_count,
                (SELECT COUNT(*) FROM post_categories WHERE post_id = p.id) as category_count,
                (SELECT COUNT(*) FROM post_tags WHERE post_id = p.id) as tag_count,
                LENGTH(p.content) as content_length,
                p.processed
            FROM posts p
            JOIN jobs j ON p.job_id = j.id
            $whereClause
            ORDER BY p.date_published DESC
        ", $params);
        
        foreach ($postStats as &$post) {
            // Calculate word count
            $post['word_count'] = str_word_count(strip_tags($post['content']));
            
            // Calculate reading time
            $post['reading_time'] = ceil($post['word_count'] / 200); // Average reading speed: 200 words per minute
            
            // Get metadata
            $metadata = $this->db->getValue("SELECT metadata FROM posts WHERE id = ?", [$post['id']]);
            $post['metadata'] = $metadata ? json_decode($metadata, true) : [];
        }
        
        $stats['posts'] = $postStats;
        
        // Get overall statistics
        $stats['total_posts'] = count($postStats);
        $stats['processed_posts'] = count(array_filter($postStats, function($post) {
            return $post['processed'] == 1;
        }));
        $stats['avg_word_count'] = array_sum(array_column($postStats, 'word_count')) / max(1, count($postStats));
        $stats['avg_reading_time'] = array_sum(array_column($postStats, 'reading_time')) / max(1, count($postStats));
        $stats['avg_image_count'] = array_sum(array_column($postStats, 'image_count')) / max(1, count($postStats));
        
        return $stats;
    }
    
    /**
     * Generate a report
     * 
     * @param string $reportType Type of report
     * @param array $options Report options
     * @return array Report data
     */
    public function generateReport($reportType, $options = []) {
        $report = [
            'type' => $reportType,
            'generated_at' => date('Y-m-d H:i:s'),
            'options' => $options,
            'data' => []
        ];
        
        switch ($reportType) {
            case 'job_summary':
                $report['data'] = $this->getJobStats();
                break;
                
            case 'post_summary':
                $report['data'] = $this->getPostStats();
                break;
                
            case 'storage_usage':
                $report['data'] = $this->calculateStorageUsage();
                break;
                
            case 'user_activity':
                $userId = $options['user_id'] ?? null;
                $limit = $options['limit'] ?? 100;
                $offset = $options['offset'] ?? 0;
                
                $userManager = new UserManager($this->db);
                $report['data'] = $userManager->getActivityLogs($userId, $limit, $offset);
                break;
                
            case 'content_analysis':
                // Analyze content across all posts
                $report['data'] = $this->analyzeContent();
                break;
        }
        
        // Log report generation
        $this->db->insert('reports', [
            'type' => $reportType,
            'options' => json_encode($options),
            'data' => json_encode($report['data']),
            'created_at' => date('Y-m-d H:i:s'),
            'user_id' => $_SESSION['user_id'] ?? null
        ]);
        
        return $report;
    }
    
    /**
     * Analyze content across all posts
     * 
     * @return array Content analysis data
     */
    private function analyzeContent() {
        $analysis = [
            'word_count' => [
                'total' => 0,
                'average' => 0,
                'min' => 0,
                'max' => 0
            ],
            'reading_time' => [
                'total' => 0,
                'average' => 0,
                'min' => 0,
                'max' => 0
            ],
            'image_count' => [
                'total' => 0,
                'average' => 0,
                'min' => 0,
                'max' => 0
            ],
            'top_keywords' => [],
            'post_distribution' => [
                'by_month' => [],
                'by_category' => [],
                'by_tag' => []
            ]
        ];
        
        // Get all posts
        $posts = $this->db->query("SELECT id, content FROM posts");
        
        // Initialize min values
        $analysis['word_count']['min'] = PHP_INT_MAX;
        $analysis['reading_time']['min'] = PHP_INT_MAX;
        $analysis['image_count']['min'] = PHP_INT_MAX;
        
        // Process each post
        $wordCounts = [];
        $readingTimes = [];
        $imageCounts = [];
        $allWords = [];
        
        foreach ($posts as $post) {
            // Calculate word count
            $content = strip_tags($post['content']);
            $wordCount = str_word_count($content);
            $wordCounts[] = $wordCount;
            
            // Update word count stats
            $analysis['word_count']['total'] += $wordCount;
            $analysis['word_count']['min'] = min($analysis['word_count']['min'], $wordCount);
            $analysis['word_count']['max'] = max($analysis['word_count']['max'], $wordCount);
            
            // Calculate reading time
            $readingTime = ceil($wordCount / 200);
            $readingTimes[] = $readingTime;
            
            // Update reading time stats
            $analysis['reading_time']['total'] += $readingTime;
            $analysis['reading_time']['min'] = min($analysis['reading_time']['min'], $readingTime);
            $analysis['reading_time']['max'] = max($analysis['reading_time']['max'], $readingTime);
            
            // Get image count
            $imageCount = $this->db->getValue("SELECT COUNT(*) FROM images WHERE post_id = ?", [$post['id']]);
            $imageCounts[] = $imageCount;
            
            // Update image count stats
            $analysis['image_count']['total'] += $imageCount;
            $analysis['image_count']['min'] = min($analysis['image_count']['min'], $imageCount);
            $analysis['image_count']['max'] = max($analysis['image_count']['max'], $imageCount);
            
            // Extract words for keyword analysis
            $text = strtolower($content);
            $text = preg_replace('/[^\p{L}\p{N}\s]/u', '', $text);
            $words = explode(' ', $text);
            $words = array_filter($words, function($word) {
                return strlen($word) > 3;
            });
            
            $allWords = array_merge($allWords, $words);
        }
        
        // Calculate averages
        $postCount = count($posts);
        if ($postCount > 0) {
            $analysis['word_count']['average'] = round($analysis['word_count']['total'] / $postCount, 2);
            $analysis['reading_time']['average'] = round($analysis['reading_time']['total'] / $postCount, 2);
            $analysis['image_count']['average'] = round($analysis['image_count']['total'] / $postCount, 2);
        }
        
        // If no posts were found, set min to 0
        if ($analysis['word_count']['min'] == PHP_INT_MAX) {
            $analysis['word_count']['min'] = 0;
            $analysis['reading_time']['min'] = 0;
            $analysis['image_count']['min'] = 0;
        }
        
        // Get top keywords
        $wordFrequency = array_count_values($allWords);
        
        // Remove common words (simple stopwords list)
        $stopwords = ['this', 'that', 'these', 'those', 'with', 'from', 'have', 'will', 'what', 'when', 'where', 'which', 'their', 'there', 'about'];
        foreach ($stopwords as $stopword) {
            unset($wordFrequency[$stopword]);
        }
        
        // Get top keywords
        arsort($wordFrequency);
        $analysis['top_keywords'] = array_slice($wordFrequency, 0, 20, true);
        
        // Get post distribution by month
        $postsByMonth = $this->db->query("
            SELECT 
                DATE_FORMAT(date_published, '%Y-%m') as month,
                COUNT(*) as count
            FROM posts
            GROUP BY month
            ORDER BY month
        ");
        
        $analysis['post_distribution']['by_month'] = $postsByMonth;
        
        // Get post distribution by category
        $postsByCategory = $this->db->query("
            SELECT 
                c.name,
                COUNT(pc.post_id) as count
            FROM categories c
            JOIN post_categories pc ON c.id = pc.category_id
            GROUP BY c.id
            ORDER BY count DESC
        ");
        
        $analysis['post_distribution']['by_category'] = $postsByCategory;
        
        // Get post distribution by tag
        $postsByTag = $this->db->query("
            SELECT 
                t.name,
                COUNT(pt.post_id) as count
            FROM tags t
            JOIN post_tags pt ON t.id = pt.tag_id
            GROUP BY t.id
            ORDER BY count DESC
        ");
        
        $analysis['post_distribution']['by_tag'] = $postsByTag;
        
        return $analysis;
    }
    
    /**
     * Format bytes to human-readable format
     * 
     * @param int $bytes Bytes
     * @param int $precision Decimal precision
     * @return string Formatted size
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, $precision) . ' ' . $units[$pow];
    }
}
