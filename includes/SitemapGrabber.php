<?php
/**
 * Sitemap Content Grabber
 *
 * This class handles grabbing content from sites using their sitemap.
 */
class SitemapGrabber extends ContentGrabber {
    /**
     * Grab content from a sitemap
     *
     * @param int $limit Maximum number of posts to grab
     * @return array Grabbed content
     */
    public function grab($limit = 10) {
        $posts = [];
        $urls = $this->extractUrlsFromSitemap($this->url);

        // Apply date filter if specified
        if (!empty($this->options['after_date'])) {
            $afterTimestamp = strtotime($this->options['after_date']);
            $urls = array_filter($urls, function($url) use ($afterTimestamp) {
                return $url['lastmod'] >= $afterTimestamp;
            });
        }

        // Apply date range filter if specified
        if (!empty($this->options['before_date'])) {
            $beforeTimestamp = strtotime($this->options['before_date']);
            $urls = array_filter($urls, function($url) use ($beforeTimestamp) {
                return $url['lastmod'] <= $beforeTimestamp;
            });
        }

        // Sort by date (newest first)
        usort($urls, function($a, $b) {
            return $b['lastmod'] - $a['lastmod'];
        });

        // Limit the number of URLs to process
        $urls = array_slice($urls, 0, $limit);

        // Process each URL
        foreach ($urls as $url) {
            $post = $this->processUrl($url['loc']);
            if ($post) {
                $posts[] = $post;
            }
        }

        return $posts;
    }

    /**
     * Extract URLs from a sitemap
     *
     * @param string $sitemapUrl URL of the sitemap
     * @return array Array of URLs with metadata
     */
    protected function extractUrlsFromSitemap($sitemapUrl) {
        $urls = [];
        $response = self::fetchUrl($sitemapUrl);

        if (!$response) {
            return $urls;
        }

        // Load the XML
        libxml_use_internal_errors(true);
        $xml = simplexml_load_string($response);
        $errors = libxml_get_errors();
        libxml_clear_errors();

        if ($xml === false || count($errors) > 0) {
            return $urls;
        }

        // Check if it's a sitemap index
        if ($xml->getName() === 'sitemapindex') {
            foreach ($xml->sitemap as $sitemap) {
                $sitemapLoc = (string)$sitemap->loc;
                $urls = array_merge($urls, $this->extractUrlsFromSitemap($sitemapLoc));
            }
        }
        // Check if it's a urlset
        elseif ($xml->getName() === 'urlset') {
            foreach ($xml->url as $url) {
                $loc = (string)$url->loc;
                $lastmod = isset($url->lastmod) ? strtotime((string)$url->lastmod) : time();

                $urls[] = [
                    'loc' => $loc,
                    'lastmod' => $lastmod
                ];
            }
        }

        return $urls;
    }

    /**
     * Process a URL to extract content
     *
     * @param string $url URL to process
     * @return array|bool Processed post data or false on failure
     */
    protected function processUrl($url) {
        $response = self::fetchUrl($url);

        if (!$response) {
            return false;
        }

        // Parse the HTML
        $dom = new DOMDocument();
        @$dom->loadHTML(mb_convert_encoding($response, 'HTML-ENTITIES', 'UTF-8'));
        $xpath = new DOMXPath($dom);

        // Extract post ID or create a hash
        $postId = md5($url);

        // Extract post slug from URL
        $urlParts = parse_url($url);
        $path = $urlParts['path'];
        $pathParts = explode('/', trim($path, '/'));
        $slug = end($pathParts);

        // If slug contains extension, remove it
        $slug = preg_replace('/\.[^.]+$/', '', $slug);

        // Create a unique directory for this post
        $postDir = IMAGES_DIR . '/' . $slug . '_' . $postId;

        // Extract title
        $title = '';
        $titleNode = $xpath->query('//title')->item(0);
        if ($titleNode) {
            $title = $titleNode->nodeValue;
        }

        // Try to get a better title from h1
        $h1Node = $xpath->query('//h1')->item(0);
        if ($h1Node) {
            $title = $h1Node->nodeValue;
        }

        // Extract content
        $content = '';
        $contentNode = $xpath->query('//article | //main | //div[@class="content"] | //div[@id="content"]')->item(0);
        if ($contentNode) {
            $content = $dom->saveHTML($contentNode);
        } else {
            // Fallback to body content
            $bodyNode = $xpath->query('//body')->item(0);
            if ($bodyNode) {
                $content = $dom->saveHTML($bodyNode);
            }
        }

        // Extract date
        $date = date('Y-m-d H:i:s');
        $dateNodes = $xpath->query('//time | //meta[@property="article:published_time"] | //span[contains(@class, "date")]');
        if ($dateNodes->length > 0) {
            $dateNode = $dateNodes->item(0);
            if ($dateNode->nodeName === 'time' && $dateNode->hasAttribute('datetime')) {
                $date = $dateNode->getAttribute('datetime');
            } elseif ($dateNode->nodeName === 'meta') {
                $date = $dateNode->getAttribute('content');
            } else {
                $date = $dateNode->nodeValue;
            }

            // Try to parse the date
            $timestamp = strtotime($date);
            if ($timestamp) {
                $date = date('Y-m-d H:i:s', $timestamp);
            }
        }

        // Extract excerpt
        $excerpt = '';
        $metaDescription = $xpath->query('//meta[@name="description"]')->item(0);
        if ($metaDescription) {
            $excerpt = $metaDescription->getAttribute('content');
        }

        // Initialize post data
        $post = [
            'id' => $postId,
            'title' => html_entity_decode($title),
            'content' => $content,
            'excerpt' => $excerpt,
            'date' => $date,
            'modified' => $date,
            'slug' => $slug,
            'link' => $url,
            'categories' => [],
            'tags' => [],
            'images' => [],
            'featured_image' => null
        ];

        // Extract categories and tags
        $metaKeywords = $xpath->query('//meta[@name="keywords"]')->item(0);
        if ($metaKeywords) {
            $keywords = explode(',', $metaKeywords->getAttribute('content'));
            foreach ($keywords as $keyword) {
                $keyword = trim($keyword);
                if (!empty($keyword)) {
                    $post['tags'][] = [
                        'id' => md5($keyword),
                        'name' => $keyword,
                        'slug' => strtolower(str_replace(' ', '-', $keyword))
                    ];
                }
            }
        }

        // Extract featured image
        $ogImage = $xpath->query('//meta[@property="og:image"]')->item(0);
        if ($ogImage) {
            $imageUrl = $ogImage->getAttribute('content');
            $localPath = $this->downloadImage($imageUrl, $postDir);
            if ($localPath) {
                $post['featured_image'] = [
                    'url' => $imageUrl,
                    'local_path' => $localPath,
                    'alt' => $post['title']
                ];
            }
        }

        // Extract inline images
        $images = $xpath->query('//img');
        foreach ($images as $img) {
            $src = $img->getAttribute('src');

            // Make sure the URL is absolute
            if (strpos($src, 'http') !== 0) {
                $baseUrl = $urlParts['scheme'] . '://' . $urlParts['host'];
                if (strpos($src, '/') === 0) {
                    $src = $baseUrl . $src;
                } else {
                    $basePath = dirname($urlParts['path']);
                    $src = $baseUrl . $basePath . '/' . $src;
                }
            }

            $alt = $img->getAttribute('alt');

            // Download the image
            $localPath = $this->downloadImage($src, $postDir);
            if ($localPath) {
                // Add to images array
                $post['images'][] = [
                    'url' => $src,
                    'local_path' => $localPath,
                    'alt' => $alt
                ];

                // Update the image src in the content
                $relativePath = str_replace(BASE_PATH, BASE_URL, $localPath);
                $img->setAttribute('src', $relativePath);
            }
        }

        // Update the content with modified image paths
        $post['content'] = $dom->saveHTML();

        // Generate HTML and PDF versions
        $this->generateHtmlVersion($post);
        $this->generatePdfVersion($post);

        return $post;
    }

    /**
     * Generate HTML version of the post
     *
     * @param array $post Processed post data
     * @return string Path to the HTML file
     */
    protected function generateHtmlVersion($post) {
        // Create the HTML directory if it doesn't exist
        if (!file_exists(HTML_DIR)) {
            mkdir(HTML_DIR, 0755, true);
        }

        // Create HTML file
        $htmlFile = HTML_DIR . '/' . $post['slug'] . '_' . $post['id'] . '.html';

        // Generate HTML content
        $html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($post['title']) . '</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        img {
            max-width: 100%;
            height: auto;
        }
        .meta {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-bottom: 20px;
        }
        .categories, .tags {
            margin-top: 30px;
        }
        .categories span, .tags span {
            display: inline-block;
            background: #f1f1f1;
            padding: 3px 10px;
            border-radius: 3px;
            margin-right: 5px;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <h1>' . htmlspecialchars($post['title']) . '</h1>
    <div class="meta">Published on: ' . date('F j, Y', strtotime($post['date'])) . '</div>';

    // Add featured image if available
    if ($post['featured_image']) {
        $featuredImagePath = str_replace(BASE_PATH, BASE_URL, $post['featured_image']['local_path']);
        $html .= '<div class="featured-image">
        <img src="' . $featuredImagePath . '" alt="' . htmlspecialchars($post['featured_image']['alt']) . '">
    </div>';
    }

    // Add content
    $html .= '<div class="content">' . $post['content'] . '</div>';

    // Add tags
    if (!empty($post['tags'])) {
        $html .= '<div class="tags">Tags: ';
        foreach ($post['tags'] as $tag) {
            $html .= '<span>' . htmlspecialchars($tag['name']) . '</span> ';
        }
        $html .= '</div>';
    }

    $html .= '</body>
</html>';

    // Save HTML file
    file_put_contents($htmlFile, $html);

    return $htmlFile;
    }

    /**
     * Generate PDF version of the post
     *
     * @param array $post Processed post data
     * @return string Path to the PDF file
     */
    protected function generatePdfVersion($post) {
        // Create the PDF directory if it doesn't exist
        if (!file_exists(PDF_DIR)) {
            mkdir(PDF_DIR, 0755, true);
        }

        // PDF file path
        $pdfFile = PDF_DIR . '/' . $post['slug'] . '_' . $post['id'] . '.pdf';

        // Check if PdfGenerator class exists
        if (file_exists(BASE_PATH . '/includes/PdfGenerator.php')) {
            require_once BASE_PATH . '/includes/PdfGenerator.php';

            // Generate PDF using PdfGenerator
            return PdfGenerator::generateFromPost($post, $pdfFile);
        } else {
            // Fallback to simple PDF generation
            $pdfContent = "PDF version of: " . $post['title'];
            file_put_contents($pdfFile, $pdfContent);

            return $pdfFile;
        }
    }
}
