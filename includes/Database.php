<?php
/**
 * Database class for handling all database operations
 */
class Database {
    private $conn;

    /**
     * Constructor - establishes database connection
     */
    public function __construct() {
        $this->conn = get_db_connection();
    }

    /**
     * Execute a query and return the result
     *
     * @param string $sql SQL query
     * @param array $params Parameters for prepared statement
     * @return array Query result or empty array on non-SELECT queries
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);

            // For SELECT, SHOW, DESCRIBE, and EXPLAIN queries, return results
            if (preg_match('/^(SELECT|SHOW|DESCRIBE|EXPLAIN)/i', $sql)) {
                $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
                return is_array($result) ? $result : [];
            }

            // For other queries (INSERT, UPDATE, DELETE), return empty array
            return [];
        } catch (PDOException $e) {
            // Log the error
            error_log("Database error in query: " . $e->getMessage() . " - SQL: $sql");

            // Return empty array instead of throwing exception to make the code more robust
            return [];
        }
    }

    /**
     * Get a single row from the database
     *
     * @param string $sql SQL query
     * @param array $params Parameters for prepared statement
     * @return array|null Single row result or null if not found
     */
    public function getRow($sql, $params = []) {
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result !== false ? $result : null;
        } catch (PDOException $e) {
            // Log the error
            error_log("Database error in getRow: " . $e->getMessage() . " - SQL: $sql");

            // Return null instead of throwing exception
            return null;
        }
    }

    /**
     * Get a single value from the database
     *
     * @param string $sql SQL query
     * @param array $params Parameters for prepared statement
     * @return mixed Single value result
     */
    public function getValue($sql, $params = []) {
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);

            $result = $stmt->fetchColumn();
            return $result !== false ? $result : null;
        } catch (PDOException $e) {
            // Log the error
            error_log("Database error in getValue: " . $e->getMessage() . " - SQL: $sql");

            // Rethrow the exception
            throw $e;
        }
    }

    /**
     * Insert a record into the database
     *
     * @param string $table Table name
     * @param array $data Associative array of column => value
     * @return int Last insert ID
     */
    public function insert($table, $data) {
        try {
            // Check if table exists - use direct PDO query to avoid potential issues
            try {
                $stmt = $this->conn->prepare("SHOW TABLES LIKE ?");
                $stmt->execute([$table]);
                $tableExists = $stmt->fetchColumn();

                if (!$tableExists) {
                    throw new Exception("Table '$table' does not exist");
                }
            } catch (PDOException $e) {
                // Log the error but continue - assume table exists
                error_log("Error checking if table exists: " . $e->getMessage() . " - Table: $table");
            }

            // Get table columns
            $tableColumns = $this->query("SHOW COLUMNS FROM $table");
            $validColumns = [];

            // Check if tableColumns is an array or object before iterating
            if (is_array($tableColumns) || is_object($tableColumns)) {
                foreach ($tableColumns as $column) {
                    if (isset($column['Field'])) {
                        $validColumns[$column['Field']] = true;
                    }
                }
            } else {
                // If query failed, log error and use a different approach
                error_log("Warning: Failed to get columns for table '$table', using direct insert");
                // Return direct insert without column validation
                $columns = implode(', ', array_keys($data));
                $placeholders = implode(', ', array_fill(0, count($data), '?'));

                $sql = "INSERT INTO $table ($columns) VALUES ($placeholders)";
                $stmt = $this->conn->prepare($sql);
                $stmt->execute(array_values($data));

                return $this->conn->lastInsertId();
            }

            // Filter out invalid columns
            $filteredData = [];
            foreach ($data as $column => $value) {
                if (isset($validColumns[$column])) {
                    $filteredData[$column] = $value;
                } else {
                    error_log("Warning: Column '$column' does not exist in table '$table' and will be ignored");
                }
            }

            // If no valid columns, throw exception
            if (empty($filteredData)) {
                throw new Exception("No valid columns found for table '$table'");
            }

            $columns = implode(', ', array_keys($filteredData));
            $placeholders = implode(', ', array_fill(0, count($filteredData), '?'));

            $sql = "INSERT INTO $table ($columns) VALUES ($placeholders)";
            error_log("Database: Executing SQL: $sql with values: " . json_encode(array_values($filteredData)));
            $stmt = $this->conn->prepare($sql);
            $stmt->execute(array_values($filteredData));

            $insertId = $this->conn->lastInsertId();
            error_log("Database: Insert successful, last insert ID: $insertId");
            return $insertId;
        } catch (PDOException $e) {
            // Log the error
            error_log("Database error in insert: " . $e->getMessage() . " - Table: $table");

            // Rethrow the exception
            throw $e;
        } catch (Exception $e) {
            // Log the error
            error_log("Error in insert: " . $e->getMessage() . " - Table: $table");

            // Rethrow the exception
            throw $e;
        }
    }

    /**
     * Update a record in the database
     *
     * @param string $table Table name
     * @param array $data Associative array of column => value
     * @param string $where Where clause
     * @param array $params Parameters for where clause
     * @return bool Success status
     */
    public function update($table, $data, $where, $params = []) {
        try {
            // Check if table exists - use direct PDO query to avoid potential issues
            try {
                $stmt = $this->conn->prepare("SHOW TABLES LIKE ?");
                $stmt->execute([$table]);
                $tableExists = $stmt->fetchColumn();

                if (!$tableExists) {
                    throw new Exception("Table '$table' does not exist");
                }
            } catch (PDOException $e) {
                // Log the error but continue - assume table exists
                error_log("Error checking if table exists: " . $e->getMessage() . " - Table: $table");
            }

            // Get table columns
            $tableColumns = $this->query("SHOW COLUMNS FROM $table");
            $validColumns = [];

            // Check if tableColumns is an array or object before iterating
            if (is_array($tableColumns) || is_object($tableColumns)) {
                foreach ($tableColumns as $column) {
                    if (isset($column['Field'])) {
                        $validColumns[$column['Field']] = true;
                    }
                }
            } else {
                // If query failed, log error and use a different approach
                error_log("Warning: Failed to get columns for table '$table', using direct update");
                // Return direct update without column validation
                $set = [];
                foreach ($data as $column => $value) {
                    $set[] = "$column = ?";
                }

                $sql = "UPDATE $table SET " . implode(', ', $set) . " WHERE $where";
                $stmt = $this->conn->prepare($sql);

                $values = array_values($data);
                $values = array_merge($values, $params);

                return $stmt->execute($values);
            }

            // Filter out invalid columns
            $filteredData = [];
            foreach ($data as $column => $value) {
                if (isset($validColumns[$column])) {
                    $filteredData[$column] = $value;
                } else {
                    error_log("Warning: Column '$column' does not exist in table '$table' and will be ignored");
                }
            }

            // If no valid columns, throw exception
            if (empty($filteredData)) {
                throw new Exception("No valid columns found for table '$table'");
            }

            $set = [];
            foreach ($filteredData as $column => $value) {
                $set[] = "$column = ?";
            }

            $sql = "UPDATE $table SET " . implode(', ', $set) . " WHERE $where";
            $stmt = $this->conn->prepare($sql);

            $values = array_values($filteredData);
            $values = array_merge($values, $params);

            return $stmt->execute($values);
        } catch (PDOException $e) {
            // Log the error
            error_log("Database error in update: " . $e->getMessage() . " - Table: $table");

            // Rethrow the exception
            throw $e;
        } catch (Exception $e) {
            // Log the error
            error_log("Error in update: " . $e->getMessage() . " - Table: $table");

            // Rethrow the exception
            throw $e;
        }
    }

    /**
     * Delete a record from the database
     *
     * @param string $table Table name
     * @param string $where Where clause
     * @param array $params Parameters for where clause
     * @return bool Success status
     */
    public function delete($table, $where, $params = []) {
        try {
            // Check if table exists - use direct PDO query to avoid potential issues
            try {
                $stmt = $this->conn->prepare("SHOW TABLES LIKE ?");
                $stmt->execute([$table]);
                $tableExists = $stmt->fetchColumn();

                if (!$tableExists) {
                    // If table doesn't exist, log warning and return true (nothing to delete)
                    error_log("Warning: Table '$table' does not exist for delete operation");
                    return true;
                }
            } catch (PDOException $e) {
                // Log the error but continue - assume table exists
                error_log("Error checking if table exists: " . $e->getMessage() . " - Table: $table");
            }

            $sql = "DELETE FROM $table WHERE $where";
            $stmt = $this->conn->prepare($sql);

            return $stmt->execute($params);
        } catch (PDOException $e) {
            // Log the error
            error_log("Database error in delete: " . $e->getMessage() . " - Table: $table");

            // Rethrow the exception
            throw $e;
        }
    }

    /**
     * Begin a transaction
     */
    public function beginTransaction() {
        $this->conn->beginTransaction();
    }

    /**
     * Commit a transaction
     */
    public function commit() {
        $this->conn->commit();
    }

    /**
     * Rollback a transaction
     */
    public function rollback() {
        $this->conn->rollBack();
    }

    /**
     * Check if a transaction is active
     *
     * @return bool True if a transaction is active
     */
    public function inTransaction() {
        return $this->conn->inTransaction();
    }
}
