<?php
/**
 * WordPress Content Grabber
 *
 * This class handles grabbing content from WordPress sites with REST API.
 */
class WordPressGrabber extends ContentGrabber {
    /**
     * Grab content from a WordPress site
     *
     * @param int $limit Maximum number of posts to grab
     * @return array Grabbed content
     */
    public function grab($limit = 10) {
        $posts = [];
        $page = 1;
        $perPage = min($limit, 100); // WordPress API usually limits to 100 per page
        $total = 0;

        // Debug mode
        $debug = isset($this->options['debug']) && $this->options['debug'];

        if ($debug) {
            error_log("WordPressGrabber: Starting grab with limit $limit");
        }

        // Build the API URL with filters
        $apiUrl = rtrim($this->url, '/') . '/wp-json/wp/v2/posts?per_page=' . $perPage;

        // Special handling for autogpt.net - it has future-dated posts
        $isAutogpt = (strpos($this->url, 'autogpt.net') !== false);
        if ($isAutogpt && $debug) {
            error_log("WordPressGrabber: Detected autogpt.net site, using special handling");
        }

        // Add orderby and order parameters to get the most recent posts first
        $apiUrl .= '&orderby=date&order=desc';

        // Add _embed parameter for featured images and terms (categories/tags)
        // Some WordPress sites might have issues with this parameter, so we'll make it optional
        if (!isset($this->options['disable_embed']) || !$this->options['disable_embed']) {
            $apiUrl .= '&_embed=1';
        }

        // Add category filter if specified
        if (!empty($this->options['category'])) {
            $apiUrl .= '&categories=' . $this->options['category'];
        }

        // Add date filter if specified
        if (!empty($this->options['after_date'])) {
            $apiUrl .= '&after=' . date('c', strtotime($this->options['after_date']));
        }

        // Add date range filter if specified
        if (!empty($this->options['before_date'])) {
            $apiUrl .= '&before=' . date('c', strtotime($this->options['before_date']));
        }

        // Debug mode
        $debug = isset($this->options['debug']) && $this->options['debug'];

        // Log the API URL if in debug mode
        if ($debug) {
            error_log("WordPressGrabber: API URL: $apiUrl");
        }

        // Fetch posts until we reach the limit
        while (count($posts) < $limit) {
            $pageUrl = $apiUrl . '&page=' . $page;

            if ($debug) {
                error_log("WordPressGrabber: Fetching page $page: $pageUrl");
            }

            $response = self::fetchUrl($pageUrl, ['debug' => $debug]);

            if (!$response) {
                // Log the error
                error_log("WordPressGrabber: Failed to fetch URL: $pageUrl");
                break;
            }

            $data = json_decode($response, true);

            if (empty($data) || !is_array($data)) {
                // Log the error
                error_log("WordPressGrabber: Invalid JSON response from URL: $pageUrl");
                error_log("Response: " . substr($response, 0, 500) . "...");
                break;
            }

            // Check if the response is an error message
            if (isset($data['code']) && isset($data['message'])) {
                error_log("WordPressGrabber: WordPress API error: {$data['code']} - {$data['message']}");

                // Check if it's an authentication error
                if ($data['code'] === 'rest_forbidden' && strpos($data['message'], 'status') !== false) {
                    // Try again without the status parameter
                    $pageUrl = str_replace('&status=publish,future', '', $pageUrl);
                    error_log("WordPressGrabber: Retrying without status parameter: $pageUrl");

                    $response = self::fetchUrl($pageUrl, ['debug' => $debug]);
                    if (!$response) {
                        error_log("WordPressGrabber: Failed to fetch URL after retry: $pageUrl");
                        break;
                    }

                    $data = json_decode($response, true);
                    if (empty($data) || !is_array($data)) {
                        error_log("WordPressGrabber: Invalid JSON response after retry: $pageUrl");
                        break;
                    }

                    if (isset($data['code']) && isset($data['message'])) {
                        error_log("WordPressGrabber: WordPress API error after retry: {$data['code']} - {$data['message']}");
                        break;
                    }
                } else {
                    break;
                }
            }

            foreach ($data as $post) {
                try {
                    $processedPost = $this->processPost($post);
                    if ($processedPost) {
                        $posts[] = $processedPost;
                        $total++;

                        if ($total >= $limit) {
                            break;
                        }
                    }
                } catch (Exception $e) {
                    error_log("WordPressGrabber: Error processing post: " . $e->getMessage());
                    // Continue with the next post
                    continue;
                }
            }

            // Check if we've reached the last page
            if (count($data) < $perPage) {
                break;
            }

            $page++;
        }

        return $posts;
    }

    /**
     * Process a WordPress post
     *
     * @param array $post WordPress post data
     * @return array Processed post data
     */
    protected function processPost($post) {
        // Debug mode
        $debug = isset($this->options['debug']) && $this->options['debug'];

        // Create a unique directory for this post
        $postId = $post['id'];
        $postSlug = $post['slug'];
        $postDir = IMAGES_DIR . '/' . $postSlug . '_' . $postId;

        // Check if the post date is in the future
        $postDate = strtotime($post['date']);
        $isFuture = $postDate > time();

        if ($debug) {
            error_log("WordPressGrabber: Processing post {$postId} with date {$post['date']} (future: " . ($isFuture ? 'yes' : 'no') . ")");
        }

        // Extract post data
        $processedPost = [
            'id' => $postId,
            'title' => html_entity_decode($post['title']['rendered']),
            'content' => $post['content']['rendered'],
            'excerpt' => isset($post['excerpt']['rendered']) ? $post['excerpt']['rendered'] : '',
            'date' => $post['date'],
            'modified' => $post['modified'],
            'slug' => $postSlug,
            'link' => $post['link'],
            'is_future' => $isFuture,
            'categories' => [],
            'tags' => [],
            'images' => [],
            'featured_image' => null
        ];

        // Extract categories
        if (isset($post['_embedded']['wp:term'])) {
            foreach ($post['_embedded']['wp:term'] as $terms) {
                foreach ($terms as $term) {
                    if ($term['taxonomy'] === 'category') {
                        $processedPost['categories'][] = [
                            'id' => $term['id'],
                            'name' => $term['name'],
                            'slug' => $term['slug']
                        ];
                    } elseif ($term['taxonomy'] === 'post_tag') {
                        $processedPost['tags'][] = [
                            'id' => $term['id'],
                            'name' => $term['name'],
                            'slug' => $term['slug']
                        ];
                    }
                }
            }
        }

        // Extract featured image
        if (isset($post['_embedded']['wp:featuredmedia'][0])) {
            $featuredMedia = $post['_embedded']['wp:featuredmedia'][0];
            if (isset($featuredMedia['source_url'])) {
                $localPath = $this->downloadImage($featuredMedia['source_url'], $postDir);
                if ($localPath) {
                    $processedPost['featured_image'] = [
                        'url' => $featuredMedia['source_url'],
                        'local_path' => $localPath,
                        'alt' => isset($featuredMedia['alt_text']) ? $featuredMedia['alt_text'] : '',
                        'caption' => isset($featuredMedia['caption']['rendered']) ? $featuredMedia['caption']['rendered'] : ''
                    ];
                }
            }
        } elseif (isset($post['featured_media']) && $post['featured_media'] > 0) {
            // If _embedded data is not available but we have the featured media ID,
            // try to fetch the media directly
            try {
                $mediaUrl = rtrim($this->url, '/') . '/wp-json/wp/v2/media/' . $post['featured_media'];
                $mediaResponse = self::fetchUrl($mediaUrl, ['debug' => $debug]);
                if ($mediaResponse) {
                    $mediaData = json_decode($mediaResponse, true);
                    if (isset($mediaData['source_url'])) {
                        $localPath = $this->downloadImage($mediaData['source_url'], $postDir);
                        if ($localPath) {
                            $processedPost['featured_image'] = [
                                'url' => $mediaData['source_url'],
                                'local_path' => $localPath,
                                'alt' => isset($mediaData['alt_text']) ? $mediaData['alt_text'] : '',
                                'caption' => isset($mediaData['caption']['rendered']) ? $mediaData['caption']['rendered'] : ''
                            ];
                        }
                    }
                }
            } catch (Exception $e) {
                error_log("WordPressGrabber: Error fetching featured media: " . $e->getMessage());
                // Continue without the featured image
            }
        }

        // Extract inline images
        $dom = new DOMDocument();
        @$dom->loadHTML(mb_convert_encoding($processedPost['content'], 'HTML-ENTITIES', 'UTF-8'));
        $images = $dom->getElementsByTagName('img');

        foreach ($images as $img) {
            $src = $img->getAttribute('src');
            $alt = $img->getAttribute('alt');

            // Download the image
            $localPath = $this->downloadImage($src, $postDir);
            if ($localPath) {
                // Add to images array
                $processedPost['images'][] = [
                    'url' => $src,
                    'local_path' => $localPath,
                    'alt' => $alt
                ];

                // Update the image src in the content
                $relativePath = str_replace(BASE_PATH, BASE_URL, $localPath);
                $img->setAttribute('src', $relativePath);
            }
        }

        // Handle figure tags
        $figures = $dom->getElementsByTagName('figure');
        foreach ($figures as $figure) {
            $imgs = $figure->getElementsByTagName('img');
            if ($imgs->length > 0) {
                $img = $imgs->item(0);
                $src = $img->getAttribute('src');

                // Check if we've already processed this image
                $processed = false;
                foreach ($processedPost['images'] as $image) {
                    if ($image['url'] === $src) {
                        $processed = true;
                        break;
                    }
                }

                if (!$processed) {
                    $alt = $img->getAttribute('alt');

                    // Download the image
                    $localPath = $this->downloadImage($src, $postDir);
                    if ($localPath) {
                        // Add to images array
                        $processedPost['images'][] = [
                            'url' => $src,
                            'local_path' => $localPath,
                            'alt' => $alt
                        ];

                        // Update the image src in the content
                        $relativePath = str_replace(BASE_PATH, BASE_URL, $localPath);
                        $img->setAttribute('src', $relativePath);
                    }
                }
            }
        }

        // Update the content with modified image paths
        $processedPost['content'] = $dom->saveHTML();

        // Generate HTML and PDF versions
        $this->generateHtmlVersion($processedPost);
        $this->generatePdfVersion($processedPost);

        return $processedPost;
    }

    /**
     * Generate HTML version of the post
     *
     * @param array $post Processed post data
     * @return string Path to the HTML file
     */
    protected function generateHtmlVersion($post) {
        // Create the HTML directory if it doesn't exist
        if (!file_exists(HTML_DIR)) {
            mkdir(HTML_DIR, 0755, true);
        }

        // Create HTML file
        $htmlFile = HTML_DIR . '/' . $post['slug'] . '_' . $post['id'] . '.html';

        // Generate HTML content
        $html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($post['title']) . '</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        img {
            max-width: 100%;
            height: auto;
        }
        .meta {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-bottom: 20px;
        }
        .categories, .tags {
            margin-top: 30px;
        }
        .categories span, .tags span {
            display: inline-block;
            background: #f1f1f1;
            padding: 3px 10px;
            border-radius: 3px;
            margin-right: 5px;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <h1>' . htmlspecialchars($post['title']) . '</h1>
    <div class="meta">Published on: ' . date('F j, Y', strtotime($post['date'])) . '</div>';

    // Add featured image if available
    if ($post['featured_image']) {
        $featuredImagePath = str_replace(BASE_PATH, BASE_URL, $post['featured_image']['local_path']);
        $html .= '<div class="featured-image">
        <img src="' . $featuredImagePath . '" alt="' . htmlspecialchars($post['featured_image']['alt']) . '">
    </div>';
    }

    // Add content
    $html .= '<div class="content">' . $post['content'] . '</div>';

    // Add categories
    if (!empty($post['categories'])) {
        $html .= '<div class="categories">Categories: ';
        foreach ($post['categories'] as $category) {
            $html .= '<span>' . htmlspecialchars($category['name']) . '</span> ';
        }
        $html .= '</div>';
    }

    // Add tags
    if (!empty($post['tags'])) {
        $html .= '<div class="tags">Tags: ';
        foreach ($post['tags'] as $tag) {
            $html .= '<span>' . htmlspecialchars($tag['name']) . '</span> ';
        }
        $html .= '</div>';
    }

    $html .= '</body>
</html>';

    // Save HTML file
    file_put_contents($htmlFile, $html);

    return $htmlFile;
    }

    /**
     * Generate PDF version of the post
     *
     * @param array $post Processed post data
     * @return string Path to the PDF file
     */
    protected function generatePdfVersion($post) {
        // Create the PDF directory if it doesn't exist
        if (!file_exists(PDF_DIR)) {
            mkdir(PDF_DIR, 0755, true);
        }

        // PDF file path
        $pdfFile = PDF_DIR . '/' . $post['slug'] . '_' . $post['id'] . '.pdf';

        // Check if PdfGenerator class exists
        if (file_exists(BASE_PATH . '/includes/PdfGenerator.php')) {
            require_once BASE_PATH . '/includes/PdfGenerator.php';

            // Generate PDF using PdfGenerator
            return PdfGenerator::generateFromPost($post, $pdfFile);
        } else {
            // Fallback to simple PDF generation
            $pdfContent = "PDF version of: " . $post['title'];
            file_put_contents($pdfFile, $pdfContent);

            return $pdfFile;
        }
    }
}
