<?php
/**
 * Content Processor Class
 * 
 * This class handles advanced content processing operations like:
 * - Content cleaning and formatting
 * - Image optimization
 * - Metadata extraction
 * - Content translation
 * - Readability improvements
 */
class ContentProcessor {
    private $db;
    private $post;
    private $options;
    
    /**
     * Constructor
     * 
     * @param Database $db Database instance
     * @param array $post Post data
     * @param array $options Processing options
     */
    public function __construct(Database $db, array $post, array $options = []) {
        $this->db = $db;
        $this->post = $post;
        $this->options = $options;
    }
    
    /**
     * Process the post content
     * 
     * @return array Processed post data
     */
    public function process() {
        // Make a copy of the post data
        $processedPost = $this->post;
        
        // Apply processing steps based on options
        if (!empty($this->options['clean_content'])) {
            $processedPost['content'] = $this->cleanContent($processedPost['content']);
        }
        
        if (!empty($this->options['optimize_images'])) {
            $processedPost = $this->optimizeImages($processedPost);
        }
        
        if (!empty($this->options['extract_metadata'])) {
            $processedPost = $this->extractMetadata($processedPost);
        }
        
        if (!empty($this->options['improve_readability'])) {
            $processedPost['content'] = $this->improveReadability($processedPost['content']);
        }
        
        if (!empty($this->options['translate']) && !empty($this->options['target_language'])) {
            $processedPost = $this->translateContent($processedPost, $this->options['target_language']);
        }
        
        // Save the processed post
        $this->saveProcessedPost($processedPost);
        
        return $processedPost;
    }
    
    /**
     * Clean and format content
     * 
     * @param string $content HTML content
     * @return string Cleaned content
     */
    public function cleanContent($content) {
        // Load content into DOMDocument
        $dom = new DOMDocument();
        @$dom->loadHTML(mb_convert_encoding($content, 'HTML-ENTITIES', 'UTF-8'), LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        $xpath = new DOMXPath($dom);
        
        // Remove unwanted elements
        $unwantedElements = [
            '//script',
            '//style',
            '//iframe',
            '//form',
            '//comment()',
            '//div[contains(@class, "advertisement")]',
            '//div[contains(@class, "ad-")]',
            '//div[contains(@class, "social")]',
            '//div[contains(@class, "related")]',
            '//div[contains(@class, "comment")]'
        ];
        
        foreach ($unwantedElements as $expression) {
            $elements = $xpath->query($expression);
            foreach ($elements as $element) {
                $element->parentNode->removeChild($element);
            }
        }
        
        // Fix links - make them absolute
        $links = $xpath->query('//a');
        $baseUrl = parse_url($this->post['url'], PHP_URL_SCHEME) . '://' . parse_url($this->post['url'], PHP_URL_HOST);
        
        foreach ($links as $link) {
            $href = $link->getAttribute('href');
            if (!empty($href) && strpos($href, 'http') !== 0) {
                if (strpos($href, '/') === 0) {
                    $link->setAttribute('href', $baseUrl . $href);
                } else {
                    $link->setAttribute('href', $baseUrl . '/' . $href);
                }
            }
            
            // Add target="_blank" to external links
            if (!empty($href) && strpos($href, $baseUrl) !== 0 && strpos($href, 'http') === 0) {
                $link->setAttribute('target', '_blank');
                $link->setAttribute('rel', 'noopener noreferrer');
            }
        }
        
        // Fix image paths
        $images = $xpath->query('//img');
        foreach ($images as $img) {
            $src = $img->getAttribute('src');
            if (!empty($src) && strpos($src, 'http') !== 0) {
                if (strpos($src, '/') === 0) {
                    $img->setAttribute('src', $baseUrl . $src);
                } else {
                    $img->setAttribute('src', $baseUrl . '/' . $src);
                }
            }
            
            // Add loading="lazy" for better performance
            $img->setAttribute('loading', 'lazy');
            
            // Add class for styling
            $currentClass = $img->getAttribute('class');
            $img->setAttribute('class', trim($currentClass . ' content-image'));
        }
        
        // Add heading IDs for better navigation
        $headings = $xpath->query('//h1 | //h2 | //h3 | //h4 | //h5 | //h6');
        foreach ($headings as $heading) {
            $text = $heading->textContent;
            $id = strtolower(preg_replace('/[^a-zA-Z0-9]+/', '-', $text));
            $id = trim($id, '-');
            $heading->setAttribute('id', $id);
        }
        
        // Save the cleaned content
        $content = $dom->saveHTML();
        
        // Additional text-based cleaning
        $content = preg_replace('/<p>\s*<\/p>/', '', $content); // Remove empty paragraphs
        $content = preg_replace('/\s+/', ' ', $content); // Normalize whitespace
        
        return $content;
    }
    
    /**
     * Optimize images in the post
     * 
     * @param array $post Post data
     * @return array Post with optimized images
     */
    public function optimizeImages($post) {
        // Get post images
        $images = $this->db->query("SELECT * FROM images WHERE post_id = ?", [$post['id']]);
        
        foreach ($images as $image) {
            $localPath = $image['local_path'];
            
            // Skip if file doesn't exist
            if (!file_exists($localPath)) {
                continue;
            }
            
            // Get image info
            $info = getimagesize($localPath);
            if (!$info) {
                continue;
            }
            
            // Only process JPG and PNG images
            $mime = $info['mime'];
            if ($mime != 'image/jpeg' && $mime != 'image/png') {
                continue;
            }
            
            // Create optimized version if it doesn't exist
            $optimizedPath = dirname($localPath) . '/optimized_' . basename($localPath);
            if (!file_exists($optimizedPath)) {
                // Simple resize and quality reduction for optimization
                list($width, $height) = $info;
                $maxDimension = 1200; // Max width or height
                
                if ($width > $maxDimension || $height > $maxDimension) {
                    // Calculate new dimensions
                    if ($width > $height) {
                        $newWidth = $maxDimension;
                        $newHeight = intval($height * ($maxDimension / $width));
                    } else {
                        $newHeight = $maxDimension;
                        $newWidth = intval($width * ($maxDimension / $height));
                    }
                    
                    // Create new image
                    $sourceImage = null;
                    $destImage = imagecreatetruecolor($newWidth, $newHeight);
                    
                    // Load source image based on type
                    if ($mime == 'image/jpeg') {
                        $sourceImage = imagecreatefromjpeg($localPath);
                    } elseif ($mime == 'image/png') {
                        $sourceImage = imagecreatefrompng($localPath);
                        // Preserve transparency
                        imagealphablending($destImage, false);
                        imagesavealpha($destImage, true);
                    }
                    
                    // Resize image
                    if ($sourceImage) {
                        imagecopyresampled($destImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
                        
                        // Save optimized image
                        if ($mime == 'image/jpeg') {
                            imagejpeg($destImage, $optimizedPath, 85); // 85% quality
                        } elseif ($mime == 'image/png') {
                            imagepng($destImage, $optimizedPath, 6); // Compression level 6
                        }
                        
                        // Free memory
                        imagedestroy($sourceImage);
                        imagedestroy($destImage);
                    }
                } else {
                    // Just copy the file if it's already small enough
                    copy($localPath, $optimizedPath);
                }
            }
            
            // Update image path in database if optimized version exists
            if (file_exists($optimizedPath)) {
                $this->db->update('images', [
                    'optimized_path' => $optimizedPath
                ], 'id = ?', [$image['id']]);
            }
        }
        
        return $post;
    }
    
    /**
     * Extract additional metadata from content
     * 
     * @param array $post Post data
     * @return array Post with extracted metadata
     */
    public function extractMetadata($post) {
        $content = $post['content'];
        $metadata = [];
        
        // Extract reading time
        $wordCount = str_word_count(strip_tags($content));
        $readingTime = ceil($wordCount / 200); // Average reading speed: 200 words per minute
        $metadata['reading_time'] = $readingTime;
        
        // Extract main keywords using simple frequency analysis
        $text = strtolower(strip_tags($content));
        $text = preg_replace('/[^\p{L}\p{N}\s]/u', '', $text); // Remove punctuation
        $words = explode(' ', $text);
        $words = array_filter($words, function($word) {
            return strlen($word) > 3; // Only words longer than 3 characters
        });
        
        // Count word frequency
        $wordFrequency = array_count_values($words);
        
        // Remove common words (simple stopwords list)
        $stopwords = ['this', 'that', 'these', 'those', 'with', 'from', 'have', 'will', 'what', 'when', 'where', 'which', 'their', 'there', 'about'];
        foreach ($stopwords as $stopword) {
            unset($wordFrequency[$stopword]);
        }
        
        // Get top keywords
        arsort($wordFrequency);
        $keywords = array_slice(array_keys($wordFrequency), 0, 10);
        $metadata['keywords'] = $keywords;
        
        // Store metadata in database
        $this->db->update('posts', [
            'metadata' => json_encode($metadata)
        ], 'id = ?', [$post['id']]);
        
        // Add metadata to post
        $post['metadata'] = $metadata;
        
        return $post;
    }
    
    /**
     * Improve readability of content
     * 
     * @param string $content HTML content
     * @return string Content with improved readability
     */
    public function improveReadability($content) {
        // Load content into DOMDocument
        $dom = new DOMDocument();
        @$dom->loadHTML(mb_convert_encoding($content, 'HTML-ENTITIES', 'UTF-8'), LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        $xpath = new DOMXPath($dom);
        
        // Add table of contents if there are enough headings
        $headings = $xpath->query('//h2 | //h3');
        if ($headings->length >= 3) {
            // Create table of contents
            $toc = $dom->createElement('div');
            $toc->setAttribute('class', 'table-of-contents');
            
            $tocTitle = $dom->createElement('h4', 'Table of Contents');
            $toc->appendChild($tocTitle);
            
            $tocList = $dom->createElement('ul');
            $toc->appendChild($tocList);
            
            foreach ($headings as $heading) {
                $id = $heading->getAttribute('id');
                if (empty($id)) {
                    $id = 'heading-' . uniqid();
                    $heading->setAttribute('id', $id);
                }
                
                $tocItem = $dom->createElement('li');
                $tocLink = $dom->createElement('a', $heading->textContent);
                $tocLink->setAttribute('href', '#' . $id);
                $tocItem->appendChild($tocLink);
                $tocList->appendChild($tocItem);
            }
            
            // Insert TOC at the beginning of the content
            $firstElement = $dom->getElementsByTagName('*')->item(0);
            if ($firstElement) {
                $firstElement->parentNode->insertBefore($toc, $firstElement);
            }
        }
        
        // Add "Back to top" links after each section
        $sections = $xpath->query('//h2 | //h3');
        foreach ($sections as $section) {
            $nextElement = $section->nextSibling;
            while ($nextElement && !($nextElement instanceof DOMElement && in_array(strtolower($nextElement->tagName), ['h2', 'h3']))) {
                $nextElement = $nextElement->nextSibling;
            }
            
            if ($nextElement) {
                $backToTop = $dom->createElement('p');
                $backToTop->setAttribute('class', 'back-to-top');
                
                $backToTopLink = $dom->createElement('a', '↑ Back to top');
                $backToTopLink->setAttribute('href', '#');
                $backToTopLink->setAttribute('class', 'back-to-top-link');
                
                $backToTop->appendChild($backToTopLink);
                $section->parentNode->insertBefore($backToTop, $nextElement);
            }
        }
        
        // Save the improved content
        $content = $dom->saveHTML();
        
        return $content;
    }
    
    /**
     * Translate content to target language
     * 
     * @param array $post Post data
     * @param string $targetLanguage Target language code
     * @return array Post with translated content
     */
    public function translateContent($post, $targetLanguage) {
        // This is a placeholder for translation functionality
        // In a real implementation, you would use a translation API like Google Translate or DeepL
        
        // For now, we'll just add a note about translation
        $post['content'] = '<div class="translation-notice">This content would be translated to ' . htmlspecialchars($targetLanguage) . '.</div>' . $post['content'];
        
        return $post;
    }
    
    /**
     * Save the processed post to the database
     * 
     * @param array $processedPost Processed post data
     * @return bool Success status
     */
    private function saveProcessedPost($processedPost) {
        // Update post in database
        return $this->db->update('posts', [
            'content' => $processedPost['content'],
            'processed' => 1,
            'updated_at' => date('Y-m-d H:i:s')
        ], 'id = ?', [$processedPost['id']]);
    }
}
