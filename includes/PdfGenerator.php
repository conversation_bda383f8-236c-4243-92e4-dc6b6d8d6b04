<?php
/**
 * PDF Generator Class
 * 
 * This class handles PDF generation using mPDF library.
 * Note: You need to install mPDF via Composer:
 * composer require mpdf/mpdf
 */
class PdfGenerator {
    /**
     * Generate PDF from post data
     * 
     * @param array $post Post data
     * @param string $outputPath Output path for the PDF file
     * @return string Path to the generated PDF file
     */
    public static function generateFromPost($post, $outputPath = null) {
        // Check if mPDF is installed
        if (!class_exists('\\Mpdf\\Mpdf')) {
            // Fallback to simple PDF generation
            return self::generateSimplePdf($post, $outputPath);
        }
        
        try {
            // Create mPDF instance
            $mpdf = new \Mpdf\Mpdf([
                'mode' => 'utf-8',
                'format' => 'A4',
                'margin_left' => 15,
                'margin_right' => 15,
                'margin_top' => 16,
                'margin_bottom' => 16,
                'margin_header' => 9,
                'margin_footer' => 9
            ]);
            
            // Set document metadata
            $mpdf->SetTitle($post['title']);
            $mpdf->SetAuthor('Content Grabber');
            $mpdf->SetCreator('Content Grabber');
            $mpdf->SetSubject('Content from ' . $post['url']);
            
            // Set header
            $mpdf->SetHTMLHeader('
                <div style="text-align: right; font-size: 8pt; color: #666;">
                    Content Grabber - ' . htmlspecialchars($post['title']) . '
                </div>
            ');
            
            // Set footer
            $mpdf->SetHTMLFooter('
                <div style="text-align: center; font-size: 8pt; color: #666;">
                    Page {PAGENO} of {nbpg} | Generated on ' . date('Y-m-d H:i:s') . '
                </div>
            ');
            
            // Generate CSS
            $css = '
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                }
                h1 {
                    color: #2c3e50;
                    margin-bottom: 20px;
                }
                img {
                    max-width: 100%;
                    height: auto;
                }
                .meta {
                    color: #7f8c8d;
                    font-size: 0.9em;
                    margin-bottom: 20px;
                }
                .categories, .tags {
                    margin-top: 30px;
                }
                .categories span, .tags span {
                    display: inline-block;
                    background: #f1f1f1;
                    padding: 3px 10px;
                    border-radius: 3px;
                    margin-right: 5px;
                    margin-bottom: 5px;
                }
                .featured-image {
                    margin-bottom: 20px;
                }
                .content {
                    margin-top: 20px;
                }
                blockquote {
                    border-left: 4px solid #3498db;
                    padding-left: 15px;
                    margin-left: 0;
                    color: #555;
                }
            ';
            
            // Add CSS
            $mpdf->WriteHTML($css, \Mpdf\HTMLParserMode::HEADER_CSS);
            
            // Start HTML content
            $html = '<h1>' . htmlspecialchars($post['title']) . '</h1>';
            $html .= '<div class="meta">Published on: ' . date('F j, Y', strtotime($post['date_published'])) . '</div>';
            
            // Add featured image if available
            $featuredImage = json_decode($post['featured_image'], true);
            if ($featuredImage && file_exists($featuredImage['local_path'])) {
                $html .= '<div class="featured-image">';
                $html .= '<img src="' . $featuredImage['local_path'] . '" alt="' . htmlspecialchars($featuredImage['alt'] ?? $post['title']) . '">';
                $html .= '</div>';
            }
            
            // Add content
            $html .= '<div class="content">' . $post['content'] . '</div>';
            
            // Add categories if available
            if (!empty($post['categories'])) {
                $html .= '<div class="categories">Categories: ';
                foreach ($post['categories'] as $category) {
                    $html .= '<span>' . htmlspecialchars($category['name']) . '</span> ';
                }
                $html .= '</div>';
            }
            
            // Add tags if available
            if (!empty($post['tags'])) {
                $html .= '<div class="tags">Tags: ';
                foreach ($post['tags'] as $tag) {
                    $html .= '<span>' . htmlspecialchars($tag['name']) . '</span> ';
                }
                $html .= '</div>';
            }
            
            // Write HTML to PDF
            $mpdf->WriteHTML($html, \Mpdf\HTMLParserMode::HTML_BODY);
            
            // Determine output path
            if ($outputPath === null) {
                $outputPath = PDF_DIR . '/' . $post['slug'] . '_' . $post['external_id'] . '.pdf';
            }
            
            // Create directory if it doesn't exist
            $dir = dirname($outputPath);
            if (!file_exists($dir)) {
                mkdir($dir, 0755, true);
            }
            
            // Output PDF
            $mpdf->Output($outputPath, \Mpdf\Output\Destination::FILE);
            
            return $outputPath;
        } catch (Exception $e) {
            // Log error
            error_log('PDF generation error: ' . $e->getMessage());
            
            // Fallback to simple PDF generation
            return self::generateSimplePdf($post, $outputPath);
        }
    }
    
    /**
     * Generate a simple PDF without mPDF
     * 
     * @param array $post Post data
     * @param string $outputPath Output path for the PDF file
     * @return string Path to the generated PDF file
     */
    private static function generateSimplePdf($post, $outputPath = null) {
        // Determine output path
        if ($outputPath === null) {
            $outputPath = PDF_DIR . '/' . $post['slug'] . '_' . $post['external_id'] . '.pdf';
        }
        
        // Create directory if it doesn't exist
        $dir = dirname($outputPath);
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
        }
        
        // Generate simple text content
        $content = "Title: " . $post['title'] . "\n\n";
        $content .= "URL: " . $post['url'] . "\n";
        $content .= "Date Published: " . date('F j, Y', strtotime($post['date_published'])) . "\n\n";
        
        // Add excerpt if available
        if (!empty($post['excerpt'])) {
            $content .= "Excerpt:\n" . strip_tags($post['excerpt']) . "\n\n";
        }
        
        // Add content (strip HTML tags)
        $content .= "Content:\n" . strip_tags($post['content']) . "\n\n";
        
        // Add categories if available
        if (!empty($post['categories'])) {
            $content .= "Categories: ";
            foreach ($post['categories'] as $index => $category) {
                $content .= $category['name'];
                if ($index < count($post['categories']) - 1) {
                    $content .= ", ";
                }
            }
            $content .= "\n";
        }
        
        // Add tags if available
        if (!empty($post['tags'])) {
            $content .= "Tags: ";
            foreach ($post['tags'] as $index => $tag) {
                $content .= $tag['name'];
                if ($index < count($post['tags']) - 1) {
                    $content .= ", ";
                }
            }
            $content .= "\n";
        }
        
        // Add generation info
        $content .= "\nGenerated by Content Grabber on " . date('Y-m-d H:i:s');
        
        // Write content to file
        file_put_contents($outputPath, $content);
        
        return $outputPath;
    }
}
