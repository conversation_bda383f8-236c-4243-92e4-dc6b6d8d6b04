<?php
/**
 * Enhanced WordPress Content Grabber
 *
 * This class is designed to grab content from any WordPress site with improved reliability.
 */
class EnhancedWordPressGrabber extends ContentGrabber {
    /**
     * Sanitize a string to be used as a slug
     *
     * @param string $title The string to sanitize
     * @return string Sanitized string
     */
    private function sanitize_title($title) {
        // Convert to lowercase
        $title = strtolower($title);

        // Remove special characters
        $title = preg_replace('/[^a-z0-9\s-]/', '', $title);

        // Replace spaces with hyphens
        $title = preg_replace('/\s+/', '-', $title);

        // Remove multiple hyphens
        $title = preg_replace('/-+/', '-', $title);

        // Trim hyphens from beginning and end
        $title = trim($title, '-');

        // If empty, use a default
        if (empty($title)) {
            $title = 'post';
        }

        return $title;
    }

    /**
     * Grab content from a WordPress site
     *
     * @param int $limit Maximum number of posts to grab
     * @param callable|null $progressCallback Optional callback for progress updates
     * @return array Grabbed content
     */
    public function grab($limit = 10, $progressCallback = null) {
        $posts = [];
        $page = 1;
        $perPage = min($limit, 100); // WordPress API usually limits to 100 per page
        $total = 0;
        $newPostsCount = 0;
        $updatedPostsCount = 0;
        $skippedPostsCount = 0;
        $startTime = microtime(true);
        $processedPostIds = []; // Track which post IDs we've processed
        $continuationOffset = 0; // Track where we left off for continuation

        // Debug mode
        $debug = isset($this->options['debug']) && $this->options['debug'];

        if ($debug) {
            error_log("EnhancedWordPressGrabber: Starting grab with limit $limit");
        }

        // Get job ID if available
        $jobId = isset($this->options['job_id']) ? $this->options['job_id'] : null;

        // Get the last run date for this job
        $lastRunDate = null;
        if ($jobId) {
            $jobInfo = $this->db->query("SELECT last_run, last_run_posts FROM jobs WHERE id = ?", [$jobId]);
            if (!empty($jobInfo) && isset($jobInfo[0]['last_run'])) {
                $lastRunDate = $jobInfo[0]['last_run'];
                $lastRunPosts = isset($jobInfo[0]['last_run_posts']) ? (int)$jobInfo[0]['last_run_posts'] : 0;

                // Calculate continuation offset based on last run
                if ($lastRunPosts > 0 && $lastRunPosts < $limit) {
                    $continuationOffset = $lastRunPosts;
                    if ($debug) {
                        error_log("EnhancedWordPressGrabber: Continuing from offset $continuationOffset (last run grabbed $lastRunPosts posts)");
                    }
                }

                if ($debug) {
                    error_log("EnhancedWordPressGrabber: Last run date for job $jobId: $lastRunDate");
                }
            }
        }

        // Get existing posts for this job to avoid duplicates
        $existingPosts = [];
        $existingPostsWithDates = []; // Store post IDs with their modification dates
        if ($jobId) {
            $existingPostsData = $this->db->query("SELECT external_id, date_modified FROM posts WHERE job_id = ? ORDER BY date_modified DESC", [$jobId]);
            if (!empty($existingPostsData)) {
                foreach ($existingPostsData as $post) {
                    $existingPosts[$post['external_id']] = true;
                    $existingPostsWithDates[$post['external_id']] = $post['date_modified'];
                }
                if ($debug) {
                    error_log("EnhancedWordPressGrabber: Found " . count($existingPosts) . " existing posts for job $jobId");
                }
            }
        }

        // Build the API URL with filters
        $apiUrl = rtrim($this->url, '/') . '/wp-json/wp/v2/posts?per_page=' . $perPage;

        // Add orderby and order parameters to get the most recent posts first
        $apiUrl .= '&orderby=date&order=desc';

        // Add _embed parameter for featured images and terms (categories/tags)
        // Some WordPress sites might have issues with this parameter, so we'll make it optional
        if (!isset($this->options['disable_embed']) || !$this->options['disable_embed']) {
            $apiUrl .= '&_embed=1';
        }

        // Add category filter if specified
        if (!empty($this->options['category'])) {
            $apiUrl .= '&categories=' . $this->options['category'];
        }

        // Add date filter if specified
        if (!empty($this->options['after_date'])) {
            $apiUrl .= '&after=' . date('c', strtotime($this->options['after_date']));
        }

        // Add date range filter if specified
        if (!empty($this->options['before_date'])) {
            $apiUrl .= '&before=' . date('c', strtotime($this->options['before_date']));
        }

        // Log the API URL if in debug mode
        if ($debug) {
            error_log("EnhancedWordPressGrabber: API URL: $apiUrl");
        }

        // Try different approaches for maximum compatibility
        $approaches = [
            'default' => $apiUrl,
            'no_embed' => rtrim($this->url, '/') . '/wp-json/wp/v2/posts?per_page=' . $perPage . '&orderby=date&order=desc',
            'simple' => rtrim($this->url, '/') . '/wp-json/wp/v2/posts?per_page=' . $perPage,
            'minimal' => rtrim($this->url, '/') . '/wp-json/wp/v2/posts'
        ];

        if ($debug) {
            error_log("EnhancedWordPressGrabber: Using multiple approaches for maximum compatibility");
        }

        foreach ($approaches as $approach => $baseUrl) {
            if ($debug) {
                error_log("EnhancedWordPressGrabber: Trying approach: $approach with URL: $baseUrl");
            }

            // Reset page counter for each approach
            $page = 1;

            // Fetch posts until we reach the limit
            while (count($posts) < $limit) {
                $pageUrl = $baseUrl . ($approach !== 'minimal' ? '&page=' . $page : '?page=' . $page);

                if ($debug) {
                    error_log("EnhancedWordPressGrabber: Fetching page $page: $pageUrl");
                }

                // Use direct cURL instead of fetchUrl
                $response = $this->directFetch($pageUrl);

                if (!$response) {
                    // Log the error
                    error_log("EnhancedWordPressGrabber: Failed to fetch URL: $pageUrl");
                    break;
                }

                $data = json_decode($response, true);

                if (empty($data) || !is_array($data)) {
                    // Log the error
                    error_log("EnhancedWordPressGrabber: Invalid JSON response from URL: $pageUrl");
                    error_log("Response: " . substr($response, 0, 500) . "...");
                    break;
                }

                // Check if the response is an error message
                if (isset($data['code']) && isset($data['message'])) {
                    error_log("EnhancedWordPressGrabber: WordPress API error: {$data['code']} - {$data['message']}");
                    break;
                }

                // If we got posts, process them
                if (count($data) > 0) {
                    if ($debug) {
                        error_log("EnhancedWordPressGrabber: Found " . count($data) . " posts with approach: $approach");
                    }

                    // Track our progress for status updates
                    $progressCounter = 0;
                    $totalPostsInBatch = count($data);

                    // First pass: Process new posts and updates
                    foreach ($data as $post) {
                        $progressCounter++;

                        // Skip if we've already processed this post in this run
                        if (isset($processedPostIds[$post['id']])) {
                            continue;
                        }

                        // Track that we've seen this post
                        $processedPostIds[$post['id']] = true;

                        try {
                            // Update progress
                            if ($debug) {
                                error_log("EnhancedWordPressGrabber: Processing post {$progressCounter}/{$totalPostsInBatch} (ID: {$post['id']})");
                            }

                            // Call progress callback if provided
                            if (is_callable($progressCallback)) {
                                $progressCallback([
                                    'processed' => count($processedPostIds),
                                    'new' => $newPostsCount,
                                    'updated' => $updatedPostsCount,
                                    'skipped' => $skippedPostsCount,
                                    'total_found' => $totalPostsInBatch,
                                    'current_page' => $page,
                                    'post_id' => $post['id'],
                                    'post_title' => isset($post['title']['rendered']) ? $post['title']['rendered'] : 'Unknown',
                                    'progress' => round(($progressCounter / $totalPostsInBatch) * 100)
                                ]);
                            }

                            // Skip if we already have this post
                            if (isset($existingPosts[$post['id']])) {
                                // Check if the post has been modified since we last grabbed it
                                $postModifiedTime = isset($post['modified']) ? strtotime($post['modified']) : 0;
                                $ourModifiedTime = isset($existingPostsWithDates[$post['id']]) ? strtotime($existingPostsWithDates[$post['id']]) : 0;

                                if ($postModifiedTime > $ourModifiedTime) {
                                    if ($debug) {
                                        error_log("EnhancedWordPressGrabber: Post {$post['id']} has been updated since we last grabbed it, processing");
                                    }
                                    // Process the updated post
                                    $processedPost = $this->processPost($post);
                                    if ($processedPost) {
                                        $processedPost['is_update'] = true;
                                        $posts[] = $processedPost;
                                        $total++;
                                        $updatedPostsCount++;

                                        if ($total >= $limit) {
                                            break 2; // Break out of both loops
                                        }
                                    }
                                } else {
                                    // Skip this post as we already have it and it hasn't been updated
                                    $skippedPostsCount++;
                                    if ($debug) {
                                        error_log("EnhancedWordPressGrabber: Skipping existing post {$post['id']} (not modified)");
                                    }
                                    continue;
                                }
                            } else {
                                // This is a new post, process it
                                $processedPost = $this->processPost($post);
                                if ($processedPost) {
                                    $processedPost['is_update'] = false;
                                    $posts[] = $processedPost;
                                    $total++;
                                    $newPostsCount++;

                                    if ($total >= $limit) {
                                        break 2; // Break out of both loops
                                    }
                                }
                            }
                        } catch (Exception $e) {
                            error_log("EnhancedWordPressGrabber: Error processing post: " . $e->getMessage());
                            // Continue with the next post
                            continue;
                        }
                    }

                    // If we need more posts and we have a continuation offset, try to get older posts
                    if (count($posts) < $limit && $continuationOffset > 0 && $page == 1) {
                        if ($debug) {
                            error_log("EnhancedWordPressGrabber: Continuing from previous run, need to get older posts");
                        }

                        // We need to skip ahead to find posts we haven't processed yet
                        // This is a simple approach - in a real implementation, you might want to use the offset parameter
                        $page = ceil($continuationOffset / $perPage) + 1;

                        if ($debug) {
                            error_log("EnhancedWordPressGrabber: Jumping ahead to page $page to continue from previous run");
                        }
                    }

                    // Check if we've reached the last page
                    if (count($data) < $perPage) {
                        break;
                    }

                    $page++;
                } else {
                    // No posts found with this approach
                    if ($debug) {
                        error_log("EnhancedWordPressGrabber: No posts found with approach: $approach");
                    }
                    break;
                }
            }

            // If we got posts, no need to try other approaches
            if (count($posts) > 0) {
                if ($debug) {
                    error_log("EnhancedWordPressGrabber: Successfully grabbed " . count($posts) . " posts with approach: $approach");
                }
                break;
            }
        }

        // Calculate execution time
        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 2);

        // Prepare statistics
        $stats = [
            'total' => count($posts),
            'new' => $newPostsCount,
            'updated' => $updatedPostsCount,
            'skipped' => $skippedPostsCount,
            'execution_time' => $executionTime,
            'approaches_tried' => count(array_filter($approaches, function($approach) use ($debug) {
                return $debug ? true : true; // Always return true, just using this to access $approach in the closure
            }, ARRAY_FILTER_USE_KEY))
        ];

        if ($debug) {
            error_log("EnhancedWordPressGrabber: Grab completed in $executionTime seconds");
            error_log("EnhancedWordPressGrabber: Stats - Total: {$stats['total']}, New: {$stats['new']}, Updated: {$stats['updated']}, Skipped: {$stats['skipped']}");
        }

        return [
            'posts' => $posts,
            'stats' => $stats
        ];
    }

    /**
     * Direct fetch using cURL
     *
     * @param string $url URL to fetch
     * @return string|bool Response or false on failure
     */
    protected function directFetch($url) {
        $debug = isset($this->options['debug']) && $this->options['debug'];

        if ($debug) {
            error_log("EnhancedWordPressGrabber: Direct fetching URL: $url");
        }

        // Initialize cURL
        $ch = curl_init();

        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // Increase timeout to 60 seconds
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HEADER, false);

        // Set user agent
        $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
        curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);

        // Set headers
        $headers = [
            'Accept: application/json',
            'Cache-Control: no-cache',
            'Pragma: no-cache'
        ];
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        // Execute the request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);

        if ($debug) {
            error_log("EnhancedWordPressGrabber: HTTP Code: $httpCode, Content-Type: $contentType, Response length: " . strlen($response));
        }

        // Check for errors
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            if ($debug) {
                error_log("EnhancedWordPressGrabber: cURL error: $error");
            }
            curl_close($ch);
            return false;
        }

        curl_close($ch);

        // Check if response is successful
        if ($httpCode < 200 || $httpCode >= 300) {
            if ($debug) {
                error_log("EnhancedWordPressGrabber: HTTP error: $httpCode");
            }
            return false;
        }

        // Validate JSON response
        if (strpos($contentType, 'application/json') !== false) {
            $data = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                if ($debug) {
                    error_log("EnhancedWordPressGrabber: Invalid JSON response: " . json_last_error_msg());
                    error_log("EnhancedWordPressGrabber: Response: " . substr($response, 0, 500) . "...");
                }

                // Try to clean up the response
                $response = preg_replace('/[\x00-\x1F\x7F]/', '', $response);
                $data = json_decode($response, true);
                if (json_last_error() !== JSON_ERROR_NONE && $debug) {
                    error_log("EnhancedWordPressGrabber: Still invalid JSON after cleanup: " . json_last_error_msg());
                    return false;
                }
            }
        }

        return $response;
    }

    /**
     * Process a WordPress post
     *
     * @param array $post WordPress post data
     * @return array Processed post data
     */
    protected function processPost($post) {
        // Debug mode
        $debug = isset($this->options['debug']) && $this->options['debug'];

        // Validate post data
        if (!isset($post['id']) || !isset($post['title']) || !isset($post['content'])) {
            if ($debug) {
                error_log("EnhancedWordPressGrabber: Invalid post data - missing required fields");
                error_log("EnhancedWordPressGrabber: Post data: " . json_encode($post));
            }
            return false;
        }

        // Create a unique directory for this post
        $postId = $post['id'];
        $postSlug = isset($post['slug']) ? $post['slug'] : $this->sanitize_title($post['title']['rendered']);
        $postDir = IMAGES_DIR . '/' . $postSlug . '_' . $postId;

        // Check if the post date is in the future
        $postDate = isset($post['date']) ? strtotime($post['date']) : time();
        $isFuture = $postDate > time();

        if ($debug) {
            error_log("EnhancedWordPressGrabber: Processing post {$postId} with date " .
                (isset($post['date']) ? $post['date'] : 'unknown') .
                " (future: " . ($isFuture ? 'yes' : 'no') . ")");
        }

        // Extract post data with fallbacks for missing fields
        $processedPost = [
            'id' => $postId,
            'title' => isset($post['title']['rendered']) ? html_entity_decode($post['title']['rendered']) : 'Untitled',
            'content' => isset($post['content']['rendered']) ? $post['content']['rendered'] : '',
            'excerpt' => isset($post['excerpt']['rendered']) ? $post['excerpt']['rendered'] : '',
            'date' => isset($post['date']) ? $post['date'] : date('Y-m-d H:i:s'),
            'modified' => isset($post['modified']) ? $post['modified'] : date('Y-m-d H:i:s'),
            'slug' => $postSlug,
            'link' => isset($post['link']) ? $post['link'] : '',
            'is_future' => $isFuture,
            'categories' => [],
            'tags' => [],
            'images' => [],
            'featured_image' => null
        ];

        // Extract categories
        if (isset($post['_embedded']['wp:term'])) {
            foreach ($post['_embedded']['wp:term'] as $terms) {
                foreach ($terms as $term) {
                    if ($term['taxonomy'] === 'category') {
                        $processedPost['categories'][] = [
                            'id' => $term['id'],
                            'name' => $term['name'],
                            'slug' => $term['slug']
                        ];
                    } elseif ($term['taxonomy'] === 'post_tag') {
                        $processedPost['tags'][] = [
                            'id' => $term['id'],
                            'name' => $term['name'],
                            'slug' => $term['slug']
                        ];
                    }
                }
            }
        }

        // Create directory if it doesn't exist
        if (!file_exists($postDir)) {
            mkdir($postDir, 0755, true);
        }

        // Extract featured image - try multiple methods
        $hasFeaturedImage = false;

        // Method 1: Check for embedded featured media
        if (isset($post['_embedded']['wp:featuredmedia'][0])) {
            $featuredMedia = $post['_embedded']['wp:featuredmedia'][0];
            if (isset($featuredMedia['source_url'])) {
                $imageUrl = $featuredMedia['source_url'];
                $localPath = $this->downloadImage($imageUrl, $postDir);

                if ($localPath) {
                    $processedPost['featured_image'] = [
                        'url' => $imageUrl,
                        'local_path' => $localPath,
                        'alt' => isset($featuredMedia['alt_text']) ? $featuredMedia['alt_text'] : '',
                        'caption' => isset($featuredMedia['caption']['rendered']) ? $featuredMedia['caption']['rendered'] : ''
                    ];
                    $hasFeaturedImage = true;

                    // Also add to images array with is_featured flag
                    $processedPost['images'][] = [
                        'url' => $imageUrl,
                        'local_path' => $localPath,
                        'alt' => isset($featuredMedia['alt_text']) ? $featuredMedia['alt_text'] : '',
                        'caption' => isset($featuredMedia['caption']['rendered']) ? $featuredMedia['caption']['rendered'] : '',
                        'is_featured' => true
                    ];

                    if ($debug) {
                        error_log("EnhancedWordPressGrabber: Downloaded featured image (method 1): $imageUrl to $localPath");
                    }
                } else if ($debug) {
                    error_log("EnhancedWordPressGrabber: Failed to download featured image (method 1): $imageUrl");
                }
            }
        }

        // Method 2: Check for featured_media ID and fetch directly
        if (!$hasFeaturedImage && isset($post['featured_media']) && $post['featured_media'] > 0) {
            try {
                $mediaUrl = rtrim($this->url, '/') . '/wp-json/wp/v2/media/' . $post['featured_media'];
                if ($debug) {
                    error_log("EnhancedWordPressGrabber: Fetching featured media (method 2): $mediaUrl");
                }

                $mediaResponse = $this->directFetch($mediaUrl);
                if ($mediaResponse) {
                    $mediaData = json_decode($mediaResponse, true);
                    if (isset($mediaData['source_url'])) {
                        $imageUrl = $mediaData['source_url'];
                        $localPath = $this->downloadImage($imageUrl, $postDir);

                        if ($localPath) {
                            $processedPost['featured_image'] = [
                                'url' => $imageUrl,
                                'local_path' => $localPath,
                                'alt' => isset($mediaData['alt_text']) ? $mediaData['alt_text'] : '',
                                'caption' => isset($mediaData['caption']['rendered']) ? $mediaData['caption']['rendered'] : ''
                            ];
                            $hasFeaturedImage = true;

                            // Also add to images array with is_featured flag
                            $processedPost['images'][] = [
                                'url' => $imageUrl,
                                'local_path' => $localPath,
                                'alt' => isset($mediaData['alt_text']) ? $mediaData['alt_text'] : '',
                                'caption' => isset($mediaData['caption']['rendered']) ? $mediaData['caption']['rendered'] : '',
                                'is_featured' => true
                            ];

                            if ($debug) {
                                error_log("EnhancedWordPressGrabber: Downloaded featured image (method 2): $imageUrl to $localPath");
                            }
                        } else if ($debug) {
                            error_log("EnhancedWordPressGrabber: Failed to download featured image (method 2): $imageUrl");
                        }
                    }
                }
            } catch (Exception $e) {
                if ($debug) {
                    error_log("EnhancedWordPressGrabber: Error fetching featured media (method 2): " . $e->getMessage());
                }
            }
        }

        // Method 3: Check for jetpack_featured_media_url
        if (!$hasFeaturedImage && isset($post['jetpack_featured_media_url'])) {
            $imageUrl = $post['jetpack_featured_media_url'];
            $localPath = $this->downloadImage($imageUrl, $postDir);

            if ($localPath) {
                $processedPost['featured_image'] = [
                    'url' => $imageUrl,
                    'local_path' => $localPath,
                    'alt' => $processedPost['title'],
                    'caption' => ''
                ];
                $hasFeaturedImage = true;

                // Also add to images array with is_featured flag
                $processedPost['images'][] = [
                    'url' => $imageUrl,
                    'local_path' => $localPath,
                    'alt' => $processedPost['title'],
                    'caption' => '',
                    'is_featured' => true
                ];

                if ($debug) {
                    error_log("EnhancedWordPressGrabber: Downloaded featured image (method 3): $imageUrl to $localPath");
                }
            } else if ($debug) {
                error_log("EnhancedWordPressGrabber: Failed to download featured image (method 3): $imageUrl");
            }
        }

        // Method 4: Look for image in post content with og:image meta tag
        if (!$hasFeaturedImage && !empty($post['link'])) {
            try {
                if ($debug) {
                    error_log("EnhancedWordPressGrabber: Trying to find featured image in post content (method 4): " . $post['link']);
                }

                $postContent = $this->directFetch($post['link']);
                if ($postContent) {
                    // Look for og:image meta tag
                    if (preg_match('/<meta property="og:image" content="([^"]+)"/', $postContent, $matches)) {
                        $imageUrl = $matches[1];
                        $localPath = $this->downloadImage($imageUrl, $postDir);

                        if ($localPath) {
                            $processedPost['featured_image'] = [
                                'url' => $imageUrl,
                                'local_path' => $localPath,
                                'alt' => $processedPost['title'],
                                'caption' => ''
                            ];
                            $hasFeaturedImage = true;

                            // Also add to images array with is_featured flag
                            $processedPost['images'][] = [
                                'url' => $imageUrl,
                                'local_path' => $localPath,
                                'alt' => $processedPost['title'],
                                'caption' => '',
                                'is_featured' => true
                            ];

                            if ($debug) {
                                error_log("EnhancedWordPressGrabber: Downloaded featured image (method 4): $imageUrl to $localPath");
                            }
                        } else if ($debug) {
                            error_log("EnhancedWordPressGrabber: Failed to download featured image (method 4): $imageUrl");
                        }
                    }
                }
            } catch (Exception $e) {
                if ($debug) {
                    error_log("EnhancedWordPressGrabber: Error fetching post content (method 4): " . $e->getMessage());
                }
            }
        }

        if (!$hasFeaturedImage && $debug) {
            error_log("EnhancedWordPressGrabber: No featured image found in post using any method");
        }

        // Extract and download inline images from content
        $processedPost = $this->extractInlineImages($processedPost, $postDir);

        // Generate HTML and PDF versions
        $this->generateHtmlVersion($processedPost);
        $this->generatePdfVersion($processedPost);

        return $processedPost;
    }

    /**
     * Generate HTML version of the post
     *
     * @param array $post Processed post data
     * @return string Path to the HTML file
     */
    protected function generateHtmlVersion($post) {
        // Create the HTML directory if it doesn't exist
        if (!file_exists(HTML_DIR)) {
            mkdir(HTML_DIR, 0755, true);
        }

        // Create HTML file
        $htmlFile = HTML_DIR . '/' . $post['slug'] . '_' . $post['id'] . '.html';

        // Generate HTML content
        $html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($post['title']) . '</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        img {
            max-width: 100%;
            height: auto;
        }
        .meta {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>' . htmlspecialchars($post['title']) . '</h1>
    <div class="meta">Published on: ' . date('F j, Y', strtotime($post['date'])) . '</div>
    <div class="content">' . $post['content'] . '</div>
</body>
</html>';

        // Save HTML file
        file_put_contents($htmlFile, $html);

        return $htmlFile;
    }

    /**
     * Generate PDF version of the post
     *
     * @param array $post Processed post data
     * @return string Path to the PDF file
     */
    protected function generatePdfVersion($post) {
        // Create the PDF directory if it doesn't exist
        if (!file_exists(PDF_DIR)) {
            mkdir(PDF_DIR, 0755, true);
        }

        // PDF file path
        $pdfFile = PDF_DIR . '/' . $post['slug'] . '_' . $post['id'] . '.pdf';

        // Simple PDF generation
        $pdfContent = "PDF version of: " . $post['title'];
        file_put_contents($pdfFile, $pdfContent);

        return $pdfFile;
    }

    /**
     * Extract and download inline images from post content
     *
     * @param array $post Processed post data
     * @param string $postDir Directory to save images
     * @return array Updated post data
     */
    protected function extractInlineImages($post, $postDir) {
        $debug = isset($this->options['debug']) && $this->options['debug'];

        if ($debug) {
            error_log("EnhancedWordPressGrabber: Extracting inline images from post: {$post['id']}");
        }

        // Create a DOM document
        $dom = new DOMDocument();

        // Suppress warnings during HTML loading
        libxml_use_internal_errors(true);

        // Load the HTML content
        $dom->loadHTML(mb_convert_encoding($post['content'], 'HTML-ENTITIES', 'UTF-8'));

        // Restore error handling
        libxml_clear_errors();

        // Create XPath object
        $xpath = new DOMXPath($dom);

        // Find all img tags
        $images = $xpath->query('//img');

        if ($debug) {
            error_log("EnhancedWordPressGrabber: Found " . $images->length . " inline images");
        }

        // Process each image
        foreach ($images as $img) {
            // Get the image URL
            $src = $img->getAttribute('src');

            // Skip data URLs
            if (strpos($src, 'data:') === 0) {
                if ($debug) {
                    error_log("EnhancedWordPressGrabber: Skipping data URL");
                }
                continue;
            }

            // Make sure the URL is absolute
            if (strpos($src, 'http') !== 0) {
                // Parse the post URL
                $postUrlParts = parse_url($post['link']);
                $baseUrl = $postUrlParts['scheme'] . '://' . $postUrlParts['host'];

                // Convert relative URL to absolute
                if (strpos($src, '/') === 0) {
                    // URL starts with /, it's relative to the domain root
                    $src = $baseUrl . $src;
                } else {
                    // URL is relative to the post URL
                    $basePath = isset($postUrlParts['path']) ? dirname($postUrlParts['path']) : '';
                    $src = $baseUrl . $basePath . '/' . $src;
                }

                if ($debug) {
                    error_log("EnhancedWordPressGrabber: Converted relative URL to absolute: $src");
                }
            }

            // Get alt text
            $alt = $img->getAttribute('alt');

            // Download the image
            $localPath = $this->downloadImage($src, $postDir);

            if ($localPath) {
                // Add to images array
                $post['images'][] = [
                    'url' => $src,
                    'local_path' => $localPath,
                    'alt' => $alt
                ];

                // Update the image src in the content to point to the local file
                $relativePath = str_replace(BASE_PATH, BASE_URL, $localPath);
                $img->setAttribute('src', $relativePath);

                if ($debug) {
                    error_log("EnhancedWordPressGrabber: Updated image src to: $relativePath");
                }

                // If no featured image, use the first inline image
                if (empty($post['featured_image']) && count($post['images']) === 1) {
                    $post['featured_image'] = [
                        'url' => $src,
                        'local_path' => $localPath,
                        'alt' => $alt,
                        'caption' => ''
                    ];

                    if ($debug) {
                        error_log("EnhancedWordPressGrabber: Using first inline image as featured image");
                    }
                }
            } else if ($debug) {
                error_log("EnhancedWordPressGrabber: Failed to download inline image: $src");
            }
        }

        // Update the content with the modified image paths
        $post['content'] = $dom->saveHTML();

        if ($debug) {
            error_log("EnhancedWordPressGrabber: Extracted " . count($post['images']) . " inline images");
        }

        return $post;
    }

    /**
     * Download an image
     *
     * @param string $url Image URL
     * @param string $dir Directory to save the image
     * @return string|bool Local path to the image or false on failure
     */
    protected function downloadImage($url, $dir) {
        $debug = isset($this->options['debug']) && $this->options['debug'];

        if ($debug) {
            error_log("EnhancedWordPressGrabber: Downloading image: $url to directory: $dir");
        }

        // Skip invalid URLs
        if (empty($url) || strpos($url, 'http') !== 0) {
            if ($debug) {
                error_log("EnhancedWordPressGrabber: Invalid image URL: $url");
            }
            return false;
        }

        // Create directory if it doesn't exist
        if (!file_exists($dir)) {
            if (!mkdir($dir, 0755, true) && !is_dir($dir)) {
                if ($debug) {
                    error_log("EnhancedWordPressGrabber: Failed to create directory: $dir");
                }
                return false;
            }
        }

        // Get image filename from URL
        $urlPath = parse_url($url, PHP_URL_PATH);
        $filename = basename($urlPath);

        // Clean up filename
        $filename = preg_replace('/[^a-zA-Z0-9\-\_\.]/', '', $filename);

        // If filename is empty or invalid, generate a random one
        if (empty($filename) || strpos($filename, '.') === false) {
            $filename = md5($url) . '.jpg';
        }

        // Local path
        $localPath = $dir . '/' . $filename;

        // Check if file already exists
        if (file_exists($localPath)) {
            if ($debug) {
                error_log("EnhancedWordPressGrabber: Image already exists: $localPath");
            }
            return $localPath;
        }

        // Initialize cURL
        $ch = curl_init();

        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        // Set user agent
        $userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
        curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);

        // Execute the request
        $imageData = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);

        if ($debug) {
            error_log("EnhancedWordPressGrabber: Image download HTTP Code: $httpCode, Content-Type: $contentType, Data length: " . strlen($imageData));
        }

        // Check for errors
        if (curl_errno($ch) || $httpCode < 200 || $httpCode >= 300 || empty($imageData)) {
            $error = curl_error($ch);
            if ($debug) {
                error_log("EnhancedWordPressGrabber: Failed to download image: $url, Error: $error, HTTP Code: $httpCode");
            }
            curl_close($ch);
            return false;
        }

        curl_close($ch);

        // Save image to file
        if (file_put_contents($localPath, $imageData) === false) {
            if ($debug) {
                error_log("EnhancedWordPressGrabber: Failed to save image to: $localPath");
            }
            return false;
        }

        if ($debug) {
            error_log("EnhancedWordPressGrabber: Successfully downloaded image to: $localPath");
        }

        return $localPath;
    }
}
?>
