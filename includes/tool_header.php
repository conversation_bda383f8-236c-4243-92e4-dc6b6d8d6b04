<?php
/**
 * Tool Header
 * 
 * Common header for all tool pages with improved navigation
 */

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ' . BASE_URL . '/login.php');
    exit;
}

// Get current user
$currentUser = $db->getRow("SELECT * FROM users WHERE id = ?", [$_SESSION['user_id']]);

// Get current page from filename
$currentFile = basename($_SERVER['PHP_SELF']);
$currentPage = pathinfo($currentFile, PATHINFO_FILENAME);

// Define tool navigation items
$toolNavItems = [
    [
        'id' => 'web_run_job',
        'file' => 'web_run_job.php',
        'title' => 'Web Job Runner',
        'icon' => 'play',
        'description' => 'Run jobs directly from your browser'
    ],
    [
        'id' => 'update_db',
        'file' => 'update_db.php',
        'title' => 'Update Database',
        'icon' => 'database',
        'description' => 'Run database updates to ensure compatibility'
    ],
    [
        'id' => 'cleanup',
        'file' => 'cleanup.php',
        'title' => 'Data Cleanup',
        'icon' => 'broom',
        'description' => 'Clean up posts, images, and related data'
    ],
    [
        'id' => 'fix_database',
        'file' => 'fix_database.php',
        'title' => 'Fix Database Schema',
        'icon' => 'wrench',
        'description' => 'Update database schema to support all features'
    ]
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?>Content Grabber</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/style.css">
    <style>
        .tool-nav {
            background-color: #f8f9fa;
            border-radius: 0.5rem;
        }
        .tool-nav .nav-link {
            color: #495057;
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            margin-bottom: 0.5rem;
            transition: all 0.2s ease-in-out;
        }
        .tool-nav .nav-link:hover {
            background-color: #e9ecef;
        }
        .tool-nav .nav-link.active {
            background-color: #0d6efd;
            color: white;
        }
        .tool-nav .nav-link i {
            width: 20px;
            text-align: center;
            margin-right: 8px;
        }
        .tool-content {
            background-color: #fff;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            padding: 1.5rem;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="<?php echo BASE_URL; ?>">
                <i class="fas fa-globe me-2"></i>
                Content Grabber
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>">
                            <i class="fas fa-tachometer-alt me-1"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/?page=jobs">
                            <i class="fas fa-tasks me-1"></i> Jobs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/?page=posts">
                            <i class="fas fa-newspaper me-1"></i> Posts
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/?page=analytics">
                            <i class="fas fa-chart-bar me-1"></i> Analytics
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/?page=settings">
                            <i class="fas fa-cog me-1"></i> Settings
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="toolsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-tools me-1"></i> Tools
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="toolsDropdown">
                            <?php foreach ($toolNavItems as $item): ?>
                            <li>
                                <a class="dropdown-item" href="<?php echo BASE_URL; ?>/<?php echo $item['file']; ?>">
                                    <i class="fas fa-<?php echo $item['icon']; ?> me-1"></i> <?php echo $item['title']; ?>
                                </a>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </li>
                </ul>
                <div class="d-flex">
                    <div class="dropdown">
                        <button class="btn btn-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user me-1"></i> <?php echo htmlspecialchars($currentUser['username']); ?>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/?page=settings"><i class="fas fa-user-cog me-1"></i> Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/logout.php"><i class="fas fa-sign-out-alt me-1"></i> Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-3 mb-4">
                <div class="tool-nav p-3">
                    <h5 class="mb-3">Tool Navigation</h5>
                    <div class="nav flex-column">
                        <?php foreach ($toolNavItems as $item): ?>
                        <a class="nav-link <?php echo $currentPage === $item['id'] || $currentFile === $item['file'] ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/<?php echo $item['file']; ?>">
                            <i class="fas fa-<?php echo $item['icon']; ?>"></i> <?php echo $item['title']; ?>
                        </a>
                        <?php endforeach; ?>
                        <a class="nav-link mt-3" href="<?php echo BASE_URL; ?>/?page=settings">
                            <i class="fas fa-arrow-left"></i> Back to Settings
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-9">
                <div class="tool-content">
                    <h2 class="mb-4"><?php echo $pageTitle; ?></h2>
                    
                    <?php if (isset($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($successMessage); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (isset($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($errorMessage); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Tool content will be placed here -->
