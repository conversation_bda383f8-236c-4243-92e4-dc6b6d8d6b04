<?php
/**
 * Job Manager class
 *
 * This class handles job creation, scheduling, and execution.
 */
class JobManager {
    private $db;

    /**
     * Constructor
     *
     * @param Database $db Database instance
     */
    public function __construct(Database $db) {
        $this->db = $db;
    }

    /**
     * Create a new job
     *
     * @param array $data Job data
     * @return int Job ID
     */
    public function createJob($data) {
        // Validate required fields
        $requiredFields = ['name', 'url', 'type', 'posts_per_run'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                throw new Exception("Field '$field' is required");
            }
        }

        // Set default values
        $data['status'] = 'pending';
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['last_run'] = null;

        try {
            // Check if jobs table exists - use direct query
            $tableExists = false;
            try {
                $result = $this->db->query("SHOW TABLES LIKE 'jobs'");
                $tableExists = !empty($result);
            } catch (Exception $e) {
                error_log("JobManager: Error checking if jobs table exists: " . $e->getMessage());
            }

            if (!$tableExists) {
                // Create jobs table if it doesn't exist
                $this->db->query("
                    CREATE TABLE jobs (
                        id INT(11) NOT NULL AUTO_INCREMENT,
                        name VARCHAR(255) NOT NULL,
                        url VARCHAR(255) NOT NULL,
                        category_id INT(11) DEFAULT NULL,
                        type ENUM('wordpress', 'sitemap') NOT NULL DEFAULT 'wordpress',
                        category VARCHAR(255) DEFAULT NULL,
                        disable_embed TINYINT(1) NOT NULL DEFAULT 0,
                        posts_per_run INT(11) NOT NULL DEFAULT 10,
                        after_date DATE DEFAULT NULL,
                        before_date DATE DEFAULT NULL,
                        status ENUM('pending', 'running', 'completed', 'failed') NOT NULL DEFAULT 'pending',
                        schedule_type VARCHAR(50) DEFAULT NULL,
                        frequency VARCHAR(50) DEFAULT 'daily',
                        last_run DATETIME DEFAULT NULL,
                        last_run_posts INT(11) DEFAULT 0,
                        error TEXT DEFAULT NULL,
                        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        PRIMARY KEY (id),
                        KEY category_id (category_id)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ");
                error_log("JobManager: Created jobs table");
            }

            // Check if the jobs table has the disable_embed column
            $hasDisableEmbed = false;
            $columns = $this->db->query("SHOW COLUMNS FROM jobs LIKE 'disable_embed'");
            if (!empty($columns)) {
                $hasDisableEmbed = true;
            }

            // If the column doesn't exist, remove it from the data
            if (!$hasDisableEmbed && isset($data['disable_embed'])) {
                unset($data['disable_embed']);
            }
        } catch (Exception $e) {
            // Log the error
            error_log("JobManager: Error checking jobs table: " . $e->getMessage());

            // If there's an error, assume the column doesn't exist
            if (isset($data['disable_embed'])) {
                unset($data['disable_embed']);
            }
        }

        // Insert job into database
        try {
            return $this->db->insert('jobs', $data);
        } catch (Exception $e) {
            // Log the error
            error_log("JobManager: Error inserting job: " . $e->getMessage());

            // Rethrow the exception with a more helpful message
            throw new Exception("Failed to create job: " . $e->getMessage());
        }
    }

    /**
     * Get all jobs
     *
     * @param array $filters Optional filters
     * @return array Jobs
     */
    public function getJobs($filters = []) {
        $sql = "SELECT * FROM jobs";
        $params = [];

        // Apply filters
        if (!empty($filters)) {
            $whereClauses = [];

            if (isset($filters['status'])) {
                $whereClauses[] = "status = ?";
                $params[] = $filters['status'];
            }

            if (isset($filters['type'])) {
                $whereClauses[] = "type = ?";
                $params[] = $filters['type'];
            }

            if (!empty($whereClauses)) {
                $sql .= " WHERE " . implode(' AND ', $whereClauses);
            }
        }

        $sql .= " ORDER BY created_at DESC";

        return $this->db->query($sql, $params);
    }

    /**
     * Get a job by ID
     *
     * @param int $jobId Job ID
     * @return array|bool Job data or false if not found
     */
    public function getJob($jobId) {
        $sql = "SELECT * FROM jobs WHERE id = ?";
        return $this->db->getRow($sql, [$jobId]);
    }

    /**
     * Update a job
     *
     * @param int $jobId Job ID
     * @param array $data Job data
     * @return bool Success status
     */
    public function updateJob($jobId, $data) {
        // Check if the jobs table has the disable_embed column
        try {
            $hasDisableEmbed = false;
            $columns = $this->db->query("SHOW COLUMNS FROM jobs LIKE 'disable_embed'");
            if (!empty($columns)) {
                $hasDisableEmbed = true;
            }

            // If the column doesn't exist, remove it from the data
            if (!$hasDisableEmbed && isset($data['disable_embed'])) {
                unset($data['disable_embed']);
            }
        } catch (Exception $e) {
            // If there's an error, assume the column doesn't exist
            if (isset($data['disable_embed'])) {
                unset($data['disable_embed']);
            }
        }

        return $this->db->update('jobs', $data, 'id = ?', [$jobId]);
    }

    /**
     * Delete a job and all related data
     *
     * @param int $jobId Job ID
     * @param bool $deleteRelatedData Whether to delete related data (posts, images, etc.)
     * @return array Result with status and details
     */
    public function deleteJob($jobId, $deleteRelatedData = true) {
        // Start transaction
        $this->db->beginTransaction();

        try {
            // Get job info for logging
            $job = $this->getJob($jobId);
            if (!$job) {
                throw new Exception("Job not found");
            }

            $result = [
                'success' => true,
                'job_name' => $job['name'],
                'job_id' => $jobId,
                'deleted_posts' => 0,
                'deleted_images' => 0,
                'deleted_files' => 0
            ];

            // If deleteRelatedData is true, delete all related data
            if ($deleteRelatedData) {
                // Get all posts for this job
                $posts = $this->db->query("SELECT id, slug, external_id, html_file, pdf_file FROM posts WHERE job_id = ?", [$jobId]);

                if (!empty($posts)) {
                    foreach ($posts as $post) {
                        $postId = $post['id'];

                        // Delete images from filesystem
                        $images = $this->db->query("SELECT local_path FROM images WHERE post_id = ?", [$postId]);
                        foreach ($images as $image) {
                            if (!empty($image['local_path']) && file_exists($image['local_path'])) {
                                unlink($image['local_path']);
                                $result['deleted_files']++;
                            }
                        }

                        // Delete post directory if it exists
                        $postDir = IMAGES_DIR . '/' . $post['slug'] . '_' . $post['external_id'];
                        if (is_dir($postDir)) {
                            $this->removeDirectory($postDir);
                        }

                        // Delete HTML file if it exists
                        if (!empty($post['html_file']) && file_exists($post['html_file'])) {
                            unlink($post['html_file']);
                            $result['deleted_files']++;
                        }

                        // Delete PDF file if it exists
                        if (!empty($post['pdf_file']) && file_exists($post['pdf_file'])) {
                            unlink($post['pdf_file']);
                            $result['deleted_files']++;
                        }

                        // Delete post tags
                        $this->db->delete('post_tags', 'post_id = ?', [$postId]);

                        // Delete post categories
                        $this->db->delete('post_categories', 'post_id = ?', [$postId]);

                        // Delete post images from database
                        $imageCount = $this->db->query("SELECT COUNT(*) as count FROM images WHERE post_id = ?", [$postId])[0]['count'];
                        $this->db->delete('images', 'post_id = ?', [$postId]);
                        $result['deleted_images'] += $imageCount;
                    }

                    // Delete all posts for this job
                    $this->db->delete('posts', 'job_id = ?', [$jobId]);
                    $result['deleted_posts'] = count($posts);
                }

                // Delete job statistics
                try {
                    $this->db->delete('job_stats', 'job_id = ?', [$jobId]);
                } catch (Exception $e) {
                    // Ignore if table doesn't exist
                }

                // Delete job runs
                try {
                    $this->db->delete('job_runs', 'job_id = ?', [$jobId]);
                } catch (Exception $e) {
                    // Ignore if table doesn't exist
                }
            }

            // Finally, delete the job itself
            $this->db->delete('jobs', 'id = ?', [$jobId]);

            // Commit transaction
            $this->db->commit();

            return $result;
        } catch (Exception $e) {
            // Rollback transaction on error
            $this->db->rollback();

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'job_id' => $jobId
            ];
        }
    }

    /**
     * Recursively remove a directory and its contents
     *
     * @param string $dir Directory path
     * @return bool Success status
     */
    public function removeDirectory($dir) {
        if (!is_dir($dir)) {
            return false;
        }

        $files = array_diff(scandir($dir), ['.', '..']);

        foreach ($files as $file) {
            $path = $dir . '/' . $file;

            if (is_dir($path)) {
                $this->removeDirectory($path);
            } else {
                unlink($path);
            }
        }

        return rmdir($dir);
    }

    /**
     * Run a job
     *
     * @param int $jobId Job ID
     * @return array Result data
     */
    public function runJob($jobId) {
        // Get job data
        $job = $this->getJob($jobId);
        if (!$job) {
            throw new Exception("Job not found");
        }

        // Create job run record if the table exists
        try {
            $runId = $this->db->insert('job_runs', [
                'job_id' => $jobId,
                'status' => 'running',
                'start_time' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            // Ignore error if job_runs table doesn't exist yet
            $runId = null;
            error_log("JobManager: job_runs table not found. Run the database update script.");
        }

        // Update job status
        $this->updateJob($jobId, [
            'status' => 'running',
            'last_run' => date('Y-m-d H:i:s')
        ]);

        try {
            // Log start of job
            error_log("JobManager: Starting job {$job['name']} (ID: $jobId)");

            // Check if URL is accessible
            $response = ContentGrabber::fetchUrl($job['url']);
            if (!$response) {
                throw new Exception("URL is not accessible: {$job['url']}");
            }

            // Prepare options for the grabber
            $options = [
                'category' => $job['category'] ?? null,
                'after_date' => $job['after_date'] ?? null,
                'before_date' => $job['before_date'] ?? null,
                'debug' => true, // Enable debug mode to help diagnose issues
                'job_id' => $jobId // Pass job ID to the grabber for smart continuation
            ];

            // Add disable_embed option for WordPress sites
            // This helps with sites that have issues with the _embed parameter
            if ($job['type'] === 'wordpress' && isset($job['disable_embed']) && $job['disable_embed']) {
                $options['disable_embed'] = true;
            }

            // Create grabber
            $grabber = ContentGrabber::create($this->db, $job['url'], $options);

            // Set up progress tracking
            $progressStartTime = microtime(true);
            $progressLastUpdate = 0;
            $progressCallback = function($status) use ($jobId, $job, $runId, &$progressStartTime, &$progressLastUpdate) {
                // Only update progress every 2 seconds to avoid too many database writes
                $now = microtime(true);
                if ($now - $progressLastUpdate < 2) {
                    return;
                }

                $progressLastUpdate = $now;
                $elapsedTime = round($now - $progressStartTime, 2);

                // Update job status with progress information
                $this->updateJob($jobId, [
                    'status' => 'running',
                    'error' => json_encode([
                        'progress' => $status,
                        'elapsed_time' => $elapsedTime,
                        'timestamp' => date('Y-m-d H:i:s')
                    ])
                ]);

                // Update job run record if it exists
                if ($runId) {
                    try {
                        $this->db->update('job_runs', [
                            'status' => 'running',
                            'posts_grabbed' => $status['processed'] ?? 0,
                            'error' => json_encode([
                                'progress' => $status,
                                'elapsed_time' => $elapsedTime
                            ])
                        ], 'id = ?', [$runId]);
                    } catch (Exception $e) {
                        // Ignore error
                    }
                }

                error_log("JobManager: Progress for job {$job['name']} (ID: $jobId) - " .
                    "Processed: " . ($status['processed'] ?? 0) . ", " .
                    "New: " . ($status['new'] ?? 0) . ", " .
                    "Updated: " . ($status['updated'] ?? 0) . ", " .
                    "Skipped: " . ($status['skipped'] ?? 0) . ", " .
                    "Time: {$elapsedTime}s");
            };

            // Grab content
            error_log("JobManager: Grabbing content for job {$job['name']} (ID: $jobId)");
            $result = $grabber->grab($job['posts_per_run'], $progressCallback);

            // Extract posts and stats
            $posts = $result['posts'] ?? [];
            $stats = $result['stats'] ?? [
                'total' => count($posts),
                'new' => count($posts),
                'updated' => 0,
                'skipped' => 0,
                'execution_time' => 0,
                'approaches_tried' => 1
            ];

            // Log statistics
            error_log("JobManager: Job statistics - Total: {$stats['total']}, New: {$stats['new']}, Updated: {$stats['updated']}, Skipped: {$stats['skipped']}, Time: {$stats['execution_time']}s");

            if (empty($posts)) {
                error_log("JobManager: No posts found for job {$job['name']} (ID: $jobId)");
            } else {
                error_log("JobManager: Found " . count($posts) . " posts for job {$job['name']} (ID: $jobId)");
            }

            // Save posts to database
            $savedPosts = $this->savePosts($posts, $jobId);

            // Store statistics in job_stats table if it exists
            try {
                $this->db->insert('job_stats', [
                    'job_id' => $jobId,
                    'run_date' => date('Y-m-d H:i:s'),
                    'total_posts' => $stats['total'],
                    'new_posts' => $stats['new'],
                    'updated_posts' => $stats['updated'],
                    'skipped_posts' => $stats['skipped'],
                    'execution_time' => $stats['execution_time'],
                    'approaches_tried' => $stats['approaches_tried']
                ]);
            } catch (Exception $e) {
                // Ignore error if job_stats table doesn't exist yet
                error_log("JobManager: job_stats table not found. Run the database update script.");
            }

            // Update job status
            $this->updateJob($jobId, [
                'status' => 'completed',
                'last_run_posts' => count($savedPosts)
            ]);

            // Update job run record if it exists
            if ($runId) {
                try {
                    $this->db->update('job_runs', [
                        'status' => 'completed',
                        'posts_grabbed' => count($savedPosts),
                        'end_time' => date('Y-m-d H:i:s')
                    ], 'id = ?', [$runId]);
                } catch (Exception $e) {
                    // Ignore error
                }
            }

            error_log("JobManager: Job {$job['name']} (ID: $jobId) completed successfully. Saved " . count($savedPosts) . " posts.");

            return [
                'success' => true,
                'posts_count' => count($savedPosts),
                'job_id' => $jobId,
                'stats' => $stats
            ];
        } catch (Exception $e) {
            // Log error
            error_log("JobManager: Error running job {$job['name']} (ID: $jobId): " . $e->getMessage());

            // Update job status
            $this->updateJob($jobId, [
                'status' => 'failed',
                'error' => $e->getMessage()
            ]);

            // Update job run record if it exists
            if ($runId) {
                try {
                    $this->db->update('job_runs', [
                        'status' => 'failed',
                        'end_time' => date('Y-m-d H:i:s'),
                        'error' => $e->getMessage()
                    ], 'id = ?', [$runId]);
                } catch (Exception $e) {
                    // Ignore error
                }
            }

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'job_id' => $jobId
            ];
        }
    }

    /**
     * Save posts to database
     *
     * @param array $posts Posts data or result array with posts
     * @param int $jobId Job ID
     * @return array Saved posts IDs
     */
    protected function savePosts($posts, $jobId) {
        $savedPosts = [];

        // Check if we have the new format (array with 'posts' and 'stats')
        if (is_array($posts) && isset($posts['posts'])) {
            $posts = $posts['posts'];
        }

        // If posts is empty or not an array, return empty array
        if (empty($posts) || !is_array($posts)) {
            return $savedPosts;
        }

        foreach ($posts as $post) {
            // Check if post already exists
            $existingPost = $this->db->getRow("SELECT id FROM posts WHERE external_id = ? OR url = ?", [
                $post['id'],
                $post['link']
            ]);

            if ($existingPost) {
                // Update existing post
                $postId = $existingPost['id'];
                $this->db->update('posts', [
                    'title' => $post['title'],
                    'content' => $post['content'],
                    'excerpt' => $post['excerpt'],
                    'date_published' => date('Y-m-d H:i:s', strtotime($post['date'])),
                    'date_modified' => date('Y-m-d H:i:s', strtotime($post['modified'])),
                    'featured_image' => $post['featured_image'] ? json_encode($post['featured_image']) : null,
                    'html_file' => HTML_DIR . '/' . $post['slug'] . '_' . $post['id'] . '.html',
                    'pdf_file' => PDF_DIR . '/' . $post['slug'] . '_' . $post['id'] . '.pdf',
                    'is_future' => isset($post['is_future']) ? $post['is_future'] : (strtotime($post['date']) > time() ? 1 : 0),
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = ?', [$postId]);
            } else {
                // Insert new post
                $postId = $this->db->insert('posts', [
                    'job_id' => $jobId,
                    'external_id' => $post['id'],
                    'title' => $post['title'],
                    'content' => $post['content'],
                    'excerpt' => $post['excerpt'],
                    'url' => $post['link'],
                    'slug' => $post['slug'],
                    'date_published' => date('Y-m-d H:i:s', strtotime($post['date'])),
                    'date_modified' => date('Y-m-d H:i:s', strtotime($post['modified'])),
                    'featured_image' => $post['featured_image'] ? json_encode($post['featured_image']) : null,
                    'html_file' => HTML_DIR . '/' . $post['slug'] . '_' . $post['id'] . '.html',
                    'pdf_file' => PDF_DIR . '/' . $post['slug'] . '_' . $post['id'] . '.pdf',
                    'is_future' => strtotime($post['date']) > time() ? 1 : 0, // Mark future-dated posts
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }

            // Save categories
            if (!empty($post['categories'])) {
                foreach ($post['categories'] as $category) {
                    // Check if category exists
                    $existingCategory = $this->db->getRow("SELECT id FROM categories WHERE name = ?", [$category['name']]);

                    if ($existingCategory) {
                        $categoryId = $existingCategory['id'];
                    } else {
                        // Insert new category
                        $categoryId = $this->db->insert('categories', [
                            'name' => $category['name'],
                            'slug' => $category['slug'],
                            'created_at' => date('Y-m-d H:i:s')
                        ]);
                    }

                    // Link post to category
                    $this->db->query("INSERT IGNORE INTO post_categories (post_id, category_id) VALUES (?, ?)", [
                        $postId,
                        $categoryId
                    ]);
                }
            }

            // Save tags
            if (!empty($post['tags'])) {
                foreach ($post['tags'] as $tag) {
                    // Check if tag exists
                    $existingTag = $this->db->getRow("SELECT id FROM tags WHERE name = ?", [$tag['name']]);

                    if ($existingTag) {
                        $tagId = $existingTag['id'];
                    } else {
                        // Insert new tag
                        $tagId = $this->db->insert('tags', [
                            'name' => $tag['name'],
                            'slug' => $tag['slug'],
                            'created_at' => date('Y-m-d H:i:s')
                        ]);
                    }

                    // Link post to tag
                    $this->db->query("INSERT IGNORE INTO post_tags (post_id, tag_id) VALUES (?, ?)", [
                        $postId,
                        $tagId
                    ]);
                }
            }

            // Save images
            if (!empty($post['images'])) {
                foreach ($post['images'] as $image) {
                    $this->db->insert('images', [
                        'post_id' => $postId,
                        'url' => $image['url'],
                        'local_path' => $image['local_path'],
                        'alt' => $image['alt'] ?? '',
                        'caption' => $image['caption'] ?? '',
                        'is_featured' => isset($image['is_featured']) && $image['is_featured'] ? 1 : 0,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                }
            }

            $savedPosts[] = $postId;
        }

        return $savedPosts;
    }

    /**
     * Run scheduled jobs
     *
     * @return array Results
     */
    public function runScheduledJobs() {
        // Get scheduled jobs
        $jobs = $this->db->query("SELECT id FROM jobs WHERE schedule_type IS NOT NULL AND status != 'running'");
        $results = [];

        foreach ($jobs as $job) {
            $results[$job['id']] = $this->runJob($job['id']);
        }

        return $results;
    }
}
