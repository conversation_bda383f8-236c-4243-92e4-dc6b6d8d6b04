/* Custom styles for Content Grabber */

/* General styles */
body {
    background-color: #f8f9fa;
    color: #333;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Card styles */
.card {
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 15px 20px;
}

.card-body {
    padding: 20px;
}

/* Button styles */
.btn {
    border-radius: 5px;
    font-weight: 500;
}

.btn-primary {
    background-color: #3498db;
    border-color: #3498db;
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.btn-success {
    background-color: #2ecc71;
    border-color: #2ecc71;
}

.btn-success:hover {
    background-color: #27ae60;
    border-color: #27ae60;
}

.btn-danger {
    background-color: #e74c3c;
    border-color: #e74c3c;
}

.btn-danger:hover {
    background-color: #c0392b;
    border-color: #c0392b;
}

/* Navbar styles */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
}

/* Table styles */
.table th {
    font-weight: 600;
    color: #555;
}

.table-hover tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

/* Form styles */
.form-label {
    font-weight: 500;
    color: #555;
}

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.25);
}

/* Alert styles */
.alert {
    border-radius: 10px;
    border: none;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

/* Badge styles */
.badge {
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 5px;
}

/* Footer styles */
footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    color: #6c757d;
}

/* Content styles */
.content img {
    max-width: 100%;
    height: auto;
    border-radius: 5px;
    margin: 10px 0;
}

.content h1, .content h2, .content h3, .content h4, .content h5, .content h6 {
    margin-top: 20px;
    margin-bottom: 10px;
    color: #2c3e50;
}

.content p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.content blockquote {
    border-left: 4px solid #3498db;
    padding-left: 15px;
    margin-left: 0;
    color: #555;
}

.content ul, .content ol {
    margin-bottom: 15px;
    padding-left: 20px;
}

.content figure {
    margin: 20px 0;
    text-align: center;
}

.content figcaption {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 5px;
}

/* Grid view styles */
.card-img-top {
    height: 200px;
    object-fit: cover;
}

/* Dashboard stats */
.display-4 {
    font-size: 3rem;
    color: #3498db;
}

/* Quick actions */
.btn-primary .fa-2x, .btn-success .fa-2x, .btn-info .fa-2x, .btn-secondary .fa-2x {
    color: rgba(255, 255, 255, 0.8);
}

/* Process page */
.modal-xl .modal-content {
    min-height: 500px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-img-top {
        height: 150px;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
}
