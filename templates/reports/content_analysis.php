<div class="report-header">
    <h3>Content Analysis Report</h3>
    <p class="text-muted">Generated on <?php echo date('F j, Y, g:i a'); ?></p>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="mb-0">Content Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Word Count</h6>
                        <table class="table table-sm">
                            <tr>
                                <th>Total</th>
                                <td><?php echo number_format($reportData['word_count']['total']); ?></td>
                            </tr>
                            <tr>
                                <th>Average</th>
                                <td><?php echo number_format($reportData['word_count']['average']); ?></td>
                            </tr>
                            <tr>
                                <th>Min</th>
                                <td><?php echo number_format($reportData['word_count']['min']); ?></td>
                            </tr>
                            <tr>
                                <th>Max</th>
                                <td><?php echo number_format($reportData['word_count']['max']); ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Reading Time (minutes)</h6>
                        <table class="table table-sm">
                            <tr>
                                <th>Total</th>
                                <td><?php echo number_format($reportData['reading_time']['total']); ?></td>
                            </tr>
                            <tr>
                                <th>Average</th>
                                <td><?php echo number_format($reportData['reading_time']['average'], 1); ?></td>
                            </tr>
                            <tr>
                                <th>Min</th>
                                <td><?php echo number_format($reportData['reading_time']['min']); ?></td>
                            </tr>
                            <tr>
                                <th>Max</th>
                                <td><?php echo number_format($reportData['reading_time']['max']); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <h6 class="mt-4">Images</h6>
                <table class="table table-sm">
                    <tr>
                        <th>Total</th>
                        <td><?php echo number_format($reportData['image_count']['total']); ?></td>
                    </tr>
                    <tr>
                        <th>Average per Post</th>
                        <td><?php echo number_format($reportData['image_count']['average'], 1); ?></td>
                    </tr>
                    <tr>
                        <th>Min</th>
                        <td><?php echo number_format($reportData['image_count']['min']); ?></td>
                    </tr>
                    <tr>
                        <th>Max</th>
                        <td><?php echo number_format($reportData['image_count']['max']); ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="mb-0">Top Keywords</h5>
            </div>
            <div class="card-body">
                <?php if (empty($reportData['top_keywords'])): ?>
                <div class="alert alert-info">No keywords found.</div>
                <?php else: ?>
                <div class="row">
                    <?php 
                    $maxCount = max($reportData['top_keywords']);
                    $keywords = array_slice($reportData['top_keywords'], 0, 20, true);
                    foreach ($keywords as $keyword => $count): 
                    ?>
                    <div class="col-md-6 mb-2">
                        <div class="d-flex justify-content-between">
                            <span><?php echo htmlspecialchars($keyword); ?></span>
                            <span class="badge bg-primary"><?php echo $count; ?></span>
                        </div>
                        <div class="progress" style="height: 5px;">
                            <div class="progress-bar" role="progressbar" style="width: <?php echo ($count / $maxCount) * 100; ?>%;" aria-valuenow="<?php echo ($count / $maxCount) * 100; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="mb-0">Post Distribution</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>By Month</h6>
                        <?php if (empty($reportData['post_distribution']['by_month'])): ?>
                        <div class="alert alert-info">No data available.</div>
                        <?php else: ?>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Month</th>
                                    <th>Posts</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($reportData['post_distribution']['by_month'] as $month): ?>
                                <tr>
                                    <td><?php echo date('F Y', strtotime($month['month'] . '-01')); ?></td>
                                    <td><?php echo $month['count']; ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        <?php endif; ?>
                    </div>
                    
                    <div class="col-md-4">
                        <h6>By Category</h6>
                        <?php if (empty($reportData['post_distribution']['by_category'])): ?>
                        <div class="alert alert-info">No data available.</div>
                        <?php else: ?>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Category</th>
                                    <th>Posts</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($reportData['post_distribution']['by_category'] as $category): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($category['name']); ?></td>
                                    <td><?php echo $category['count']; ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        <?php endif; ?>
                    </div>
                    
                    <div class="col-md-4">
                        <h6>By Tag</h6>
                        <?php if (empty($reportData['post_distribution']['by_tag'])): ?>
                        <div class="alert alert-info">No data available.</div>
                        <?php else: ?>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Tag</th>
                                    <th>Posts</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($reportData['post_distribution']['by_tag'] as $tag): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($tag['name']); ?></td>
                                    <td><?php echo $tag['count']; ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="mt-4">
    <h4>Content Analysis Summary</h4>
    <p>This report provides an in-depth analysis of the content in your database, including word counts, reading times, image usage, and keyword frequency.</p>
    
    <div class="alert alert-info">
        <strong>Insights:</strong>
        <ul class="mb-0">
            <li>The average post contains <?php echo number_format($reportData['word_count']['average']); ?> words and takes approximately <?php echo number_format($reportData['reading_time']['average'], 1); ?> minutes to read.</li>
            <li>Posts contain an average of <?php echo number_format($reportData['image_count']['average'], 1); ?> images per post.</li>
            <li>The most common keywords in your content reflect your main topics and focus areas.</li>
        </ul>
    </div>
</div>
