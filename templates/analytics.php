<?php
// Set page title
$pageTitle = 'Analytics';

// Check if Analytics class exists
if (!file_exists('includes/Analytics.php')) {
    echo '<div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle me-2"></i>
        The Analytics module is not available yet. Please run the <a href="' . BASE_URL . '/update.php">database update</a> first.
    </div>';
    exit;
}

// Initialize Analytics
require_once 'includes/Analytics.php';
$analytics = new Analytics($db);

// Get dashboard stats
try {
    $stats = $analytics->getDashboardStats();
} catch (Exception $e) {
    echo '<div class="alert alert-danger">
        <i class="fas fa-exclamation-circle me-2"></i>
        Error loading analytics: ' . htmlspecialchars($e->getMessage()) . '
        <br>
        Please run the <a href="' . BASE_URL . '/update.php">database update</a> first.
    </div>';
    exit;
}

// Handle report generation
if (isset($_GET['report'])) {
    $reportType = $_GET['report'];
    $options = [];

    // Set report options
    if (isset($_GET['user_id'])) {
        $options['user_id'] = (int)$_GET['user_id'];
    }

    if (isset($_GET['limit'])) {
        $options['limit'] = (int)$_GET['limit'];
    }

    if (isset($_GET['offset'])) {
        $options['offset'] = (int)$_GET['offset'];
    }

    // Generate report
    $report = $analytics->generateReport($reportType, $options);

    // Set report data for display
    $reportData = $report['data'];
}
?>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Analytics Dashboard</h5>
                <div class="dropdown">
                    <button class="btn btn-primary dropdown-toggle" type="button" id="reportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-file-alt me-1"></i> Generate Report
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="reportDropdown">
                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/?page=analytics&report=job_summary">Job Summary</a></li>
                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/?page=analytics&report=post_summary">Post Summary</a></li>
                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/?page=analytics&report=storage_usage">Storage Usage</a></li>
                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/?page=analytics&report=user_activity">User Activity</a></li>
                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/?page=analytics&report=content_analysis">Content Analysis</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-3">
        <div class="card mb-4 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="display-4 text-primary mb-2">
                    <i class="fas fa-tasks"></i>
                </div>
                <h5 class="card-title">Total Jobs</h5>
                <h2 class="mb-0"><?php echo $stats['total_jobs']; ?></h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card mb-4 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="display-4 text-success mb-2">
                    <i class="fas fa-newspaper"></i>
                </div>
                <h5 class="card-title">Total Posts</h5>
                <h2 class="mb-0"><?php echo $stats['total_posts']; ?></h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card mb-4 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="display-4 text-info mb-2">
                    <i class="fas fa-images"></i>
                </div>
                <h5 class="card-title">Total Images</h5>
                <h2 class="mb-0"><?php echo $stats['total_images']; ?></h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card mb-4 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="display-4 text-warning mb-2">
                    <i class="fas fa-users"></i>
                </div>
                <h5 class="card-title">Total Users</h5>
                <h2 class="mb-0"><?php echo $stats['total_users']; ?></h2>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4 border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">Recent Activity</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Action</th>
                                <th>Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($stats['recent_activity'] as $activity): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($activity['username']); ?></td>
                                <td>
                                    <?php echo htmlspecialchars($activity['action']); ?>
                                    <?php if (!empty($activity['details'])): ?>
                                    <small class="text-muted d-block"><?php echo htmlspecialchars($activity['details']); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('M d, H:i', strtotime($activity['created_at'])); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card mb-4 border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">Storage Usage</h5>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h6>Total Storage: <?php echo $stats['storage_usage']['total_formatted']; ?></h6>
                    <div class="progress">
                        <div class="progress-bar bg-primary" role="progressbar" style="width: <?php echo round(($stats['storage_usage']['images'] / max(1, $stats['storage_usage']['total'])) * 100); ?>%" aria-valuenow="<?php echo round(($stats['storage_usage']['images'] / max(1, $stats['storage_usage']['total'])) * 100); ?>" aria-valuemin="0" aria-valuemax="100">Images</div>
                        <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo round(($stats['storage_usage']['html'] / max(1, $stats['storage_usage']['total'])) * 100); ?>%" aria-valuenow="<?php echo round(($stats['storage_usage']['html'] / max(1, $stats['storage_usage']['total'])) * 100); ?>" aria-valuemin="0" aria-valuemax="100">HTML</div>
                        <div class="progress-bar bg-info" role="progressbar" style="width: <?php echo round(($stats['storage_usage']['pdf'] / max(1, $stats['storage_usage']['total'])) * 100); ?>%" aria-valuenow="<?php echo round(($stats['storage_usage']['pdf'] / max(1, $stats['storage_usage']['total'])) * 100); ?>" aria-valuemin="0" aria-valuemax="100">PDF</div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center py-3">
                                <h6 class="card-title mb-0">Images</h6>
                                <p class="card-text"><?php echo $stats['storage_usage']['images_formatted']; ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center py-3">
                                <h6 class="card-title mb-0">HTML</h6>
                                <p class="card-text"><?php echo $stats['storage_usage']['html_formatted']; ?></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center py-3">
                                <h6 class="card-title mb-0">PDF</h6>
                                <p class="card-text"><?php echo $stats['storage_usage']['pdf_formatted']; ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4 border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">Top Categories</h5>
            </div>
            <div class="card-body">
                <?php if (empty($stats['top_categories'])): ?>
                <div class="alert alert-info">No categories found.</div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Posts</th>
                                <th>Percentage</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $totalCategoryPosts = array_sum(array_column($stats['top_categories'], 'post_count'));
                            foreach ($stats['top_categories'] as $category):
                            ?>
                            <tr>
                                <td><?php echo htmlspecialchars($category['name']); ?></td>
                                <td><?php echo $category['post_count']; ?></td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" style="width: <?php echo round(($category['post_count'] / max(1, $totalCategoryPosts)) * 100); ?>%" aria-valuenow="<?php echo round(($category['post_count'] / max(1, $totalCategoryPosts)) * 100); ?>" aria-valuemin="0" aria-valuemax="100">
                                            <?php echo round(($category['post_count'] / max(1, $totalCategoryPosts)) * 100); ?>%
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card mb-4 border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">Top Tags</h5>
            </div>
            <div class="card-body">
                <?php if (empty($stats['top_tags'])): ?>
                <div class="alert alert-info">No tags found.</div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Tag</th>
                                <th>Posts</th>
                                <th>Percentage</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $totalTagPosts = array_sum(array_column($stats['top_tags'], 'post_count'));
                            foreach ($stats['top_tags'] as $tag):
                            ?>
                            <tr>
                                <td><?php echo htmlspecialchars($tag['name']); ?></td>
                                <td><?php echo $tag['post_count']; ?></td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" style="width: <?php echo round(($tag['post_count'] / max(1, $totalTagPosts)) * 100); ?>%" aria-valuenow="<?php echo round(($tag['post_count'] / max(1, $totalTagPosts)) * 100); ?>" aria-valuemin="0" aria-valuemax="100">
                                            <?php echo round(($tag['post_count'] / max(1, $totalTagPosts)) * 100); ?>%
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if (isset($reportData)): ?>
<div class="row">
    <div class="col-md-12">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Report: <?php echo ucfirst(str_replace('_', ' ', $_GET['report'])); ?></h5>
                <button class="btn btn-sm btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print me-1"></i> Print Report
                </button>
            </div>
            <div class="card-body">
                <?php
                // Display report based on type
                switch ($_GET['report']) {
                    case 'job_summary':
                        include 'templates/reports/job_summary.php';
                        break;

                    case 'post_summary':
                        include 'templates/reports/post_summary.php';
                        break;

                    case 'storage_usage':
                        include 'templates/reports/storage_usage.php';
                        break;

                    case 'user_activity':
                        include 'templates/reports/user_activity.php';
                        break;

                    case 'content_analysis':
                        include 'templates/reports/content_analysis.php';
                        break;
                }
                ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>
