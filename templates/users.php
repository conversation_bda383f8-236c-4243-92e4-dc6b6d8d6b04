<?php
// Set page title
$pageTitle = 'User Management';

// Initialize UserManager
require_once 'includes/UserManager.php';
$userManager = new UserManager($db);

// Check if current user has permission to manage users
if (!$userManager->hasPermission($_SESSION['user_id'], 'manage_users')) {
    echo '<div class="alert alert-danger">You do not have permission to manage users.</div>';
    exit;
}

// Set page actions
$pageActions = '<a href="' . BASE_URL . '/?page=users&action=new" class="btn btn-primary">
    <i class="fas fa-user-plus me-1"></i> New User
</a>';

// Handle actions
$action = isset($_GET['action']) ? $_GET['action'] : '';
$userId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($action) {
        case 'new':
            // Create new user
            try {
                $userData = [
                    'username' => $_POST['username'] ?? '',
                    'email' => $_POST['email'] ?? '',
                    'password' => $_POST['password'] ?? '',
                    'role' => $_POST['role'] ?? UserManager::ROLE_VIEWER
                ];
                
                $userId = $userManager->createUser($userData);
                
                // Log activity
                $userManager->logActivity($_SESSION['user_id'], 'create_user', "Created user: {$userData['username']}");
                
                $successMessage = 'User created successfully!';
                
                // Redirect to user list
                header('Location: ' . BASE_URL . '/?page=users&success=' . urlencode($successMessage));
                exit;
            } catch (Exception $e) {
                $errorMessage = 'Error creating user: ' . $e->getMessage();
            }
            break;
            
        case 'edit':
            // Update user
            try {
                $userData = [
                    'username' => $_POST['username'] ?? '',
                    'email' => $_POST['email'] ?? '',
                    'role' => $_POST['role'] ?? ''
                ];
                
                // Add password if provided
                if (!empty($_POST['password'])) {
                    $userData['password'] = $_POST['password'];
                }
                
                $userManager->updateUser($userId, $userData);
                
                // Log activity
                $userManager->logActivity($_SESSION['user_id'], 'update_user', "Updated user: {$userData['username']}");
                
                $successMessage = 'User updated successfully!';
                
                // Redirect to user list
                header('Location: ' . BASE_URL . '/?page=users&success=' . urlencode($successMessage));
                exit;
            } catch (Exception $e) {
                $errorMessage = 'Error updating user: ' . $e->getMessage();
            }
            break;
            
        case 'delete':
            // Delete user
            try {
                $user = $userManager->getUser($userId);
                $userManager->deleteUser($userId);
                
                // Log activity
                $userManager->logActivity($_SESSION['user_id'], 'delete_user', "Deleted user: {$user['username']}");
                
                $successMessage = 'User deleted successfully!';
                
                // Redirect to user list
                header('Location: ' . BASE_URL . '/?page=users&success=' . urlencode($successMessage));
                exit;
            } catch (Exception $e) {
                $errorMessage = 'Error deleting user: ' . $e->getMessage();
            }
            break;
            
        case 'generate_api_key':
            // Generate API key
            try {
                $apiKey = $userManager->generateApiKey($userId);
                
                // Log activity
                $userManager->logActivity($_SESSION['user_id'], 'generate_api_key', "Generated API key for user ID: $userId");
                
                $successMessage = 'API key generated successfully!';
                
                // Redirect to user edit page
                header('Location: ' . BASE_URL . '/?page=users&action=edit&id=' . $userId . '&success=' . urlencode($successMessage));
                exit;
            } catch (Exception $e) {
                $errorMessage = 'Error generating API key: ' . $e->getMessage();
            }
            break;
            
        case 'revoke_api_key':
            // Revoke API key
            try {
                $userManager->revokeApiKey($userId);
                
                // Log activity
                $userManager->logActivity($_SESSION['user_id'], 'revoke_api_key', "Revoked API key for user ID: $userId");
                
                $successMessage = 'API key revoked successfully!';
                
                // Redirect to user edit page
                header('Location: ' . BASE_URL . '/?page=users&action=edit&id=' . $userId . '&success=' . urlencode($successMessage));
                exit;
            } catch (Exception $e) {
                $errorMessage = 'Error revoking API key: ' . $e->getMessage();
            }
            break;
    }
}

// Display success/error messages
if (isset($_GET['success'])) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
        ' . htmlspecialchars($_GET['success']) . '
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>';
}

if (isset($errorMessage)) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
        ' . htmlspecialchars($errorMessage) . '
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>';
}

// Display appropriate content based on action
switch ($action) {
    case 'new':
        // New user form
        ?>
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">Create New User</h5>
            </div>
            <div class="card-body">
                <form method="post" action="<?php echo BASE_URL; ?>/?page=users&action=new">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="<?php echo UserManager::ROLE_ADMIN; ?>">Administrator</option>
                            <option value="<?php echo UserManager::ROLE_EDITOR; ?>">Editor</option>
                            <option value="<?php echo UserManager::ROLE_VIEWER; ?>" selected>Viewer</option>
                        </select>
                        <div class="form-text">
                            <strong>Administrator:</strong> Full access to all features<br>
                            <strong>Editor:</strong> Can manage jobs, run jobs, and edit posts<br>
                            <strong>Viewer:</strong> Can only view posts
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="<?php echo BASE_URL; ?>/?page=users" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i> Back to Users
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Create User
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <?php
        break;
        
    case 'edit':
        // Edit user form
        $user = $userManager->getUser($userId);
        
        if (!$user) {
            echo '<div class="alert alert-danger">User not found!</div>';
            echo '<p><a href="' . BASE_URL . '/?page=users" class="btn btn-primary">Back to Users</a></p>';
            break;
        }
        
        // Get full user data including API key
        $fullUser = $db->getRow("SELECT * FROM users WHERE id = ?", [$userId]);
        ?>
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="mb-0">Edit User: <?php echo htmlspecialchars($user['username']); ?></h5>
            </div>
            <div class="card-body">
                <form method="post" action="<?php echo BASE_URL; ?>/?page=users&action=edit&id=<?php echo $user['id']; ?>">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" value="<?php echo htmlspecialchars($user['username']); ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password">
                        <div class="form-text">Leave empty to keep current password</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="<?php echo UserManager::ROLE_ADMIN; ?>" <?php echo $user['role'] === UserManager::ROLE_ADMIN ? 'selected' : ''; ?>>Administrator</option>
                            <option value="<?php echo UserManager::ROLE_EDITOR; ?>" <?php echo $user['role'] === UserManager::ROLE_EDITOR ? 'selected' : ''; ?>>Editor</option>
                            <option value="<?php echo UserManager::ROLE_VIEWER; ?>" <?php echo $user['role'] === UserManager::ROLE_VIEWER ? 'selected' : ''; ?>>Viewer</option>
                        </select>
                        <div class="form-text">
                            <strong>Administrator:</strong> Full access to all features<br>
                            <strong>Editor:</strong> Can manage jobs, run jobs, and edit posts<br>
                            <strong>Viewer:</strong> Can only view posts
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="<?php echo BASE_URL; ?>/?page=users" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i> Back to Users
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Update User
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="mb-0">API Access</h5>
            </div>
            <div class="card-body">
                <?php if ($userManager->hasPermission($userId, 'use_api')): ?>
                    <?php if (empty($fullUser['api_key'])): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        This user doesn't have an API key yet.
                    </div>
                    
                    <form method="post" action="<?php echo BASE_URL; ?>/?page=users&action=generate_api_key&id=<?php echo $user['id']; ?>">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-key me-1"></i> Generate API Key
                        </button>
                    </form>
                    <?php else: ?>
                    <div class="mb-3">
                        <label for="api_key" class="form-label">API Key</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="api_key" value="<?php echo htmlspecialchars($fullUser['api_key']); ?>" readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="copyApiKey()">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <div class="form-text">This key provides API access for this user.</div>
                    </div>
                    
                    <form method="post" action="<?php echo BASE_URL; ?>/?page=users&action=revoke_api_key&id=<?php echo $user['id']; ?>">
                        <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to revoke this API key?')">
                            <i class="fas fa-trash me-1"></i> Revoke API Key
                        </button>
                    </form>
                    
                    <script>
                    function copyApiKey() {
                        var apiKeyInput = document.getElementById('api_key');
                        apiKeyInput.select();
                        document.execCommand('copy');
                        alert('API key copied to clipboard!');
                    }
                    </script>
                    <?php endif; ?>
                <?php else: ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    This user does not have permission to use the API. Change the user's role to Administrator or Editor to enable API access.
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">User Activity</h5>
            </div>
            <div class="card-body p-0">
                <?php
                $activities = $userManager->getActivityLogs($userId, 10);
                
                if (empty($activities)) {
                    echo '<div class="p-4 text-center">
                        <p class="text-muted mb-0">No activity found for this user.</p>
                    </div>';
                } else {
                    echo '<div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Action</th>
                                    <th>Details</th>
                                    <th>IP Address</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody>';
                    
                    foreach ($activities as $activity) {
                        echo '<tr>
                            <td>' . htmlspecialchars($activity['action']) . '</td>
                            <td>' . htmlspecialchars($activity['details']) . '</td>
                            <td>' . htmlspecialchars($activity['ip_address']) . '</td>
                            <td>' . date('M d, Y H:i', strtotime($activity['created_at'])) . '</td>
                        </tr>';
                    }
                    
                    echo '</tbody>
                        </table>
                    </div>';
                }
                ?>
            </div>
        </div>
        <?php
        break;
        
    default:
        // User list
        $users = $userManager->getUsers();
        ?>
        <div class="card border-0 shadow-sm">
            <div class="card-body p-0">
                <?php if (empty($users)): ?>
                <div class="p-4 text-center">
                    <p class="text-muted mb-4">No users found.</p>
                    <a href="<?php echo BASE_URL; ?>/?page=users&action=new" class="btn btn-primary">
                        <i class="fas fa-user-plus me-1"></i> Create New User
                    </a>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Username</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Last Login</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                <td>
                                    <?php
                                    switch ($user['role']) {
                                        case UserManager::ROLE_ADMIN:
                                            echo '<span class="badge bg-danger">Administrator</span>';
                                            break;
                                        case UserManager::ROLE_EDITOR:
                                            echo '<span class="badge bg-warning">Editor</span>';
                                            break;
                                        case UserManager::ROLE_VIEWER:
                                            echo '<span class="badge bg-info">Viewer</span>';
                                            break;
                                        default:
                                            echo '<span class="badge bg-secondary">Unknown</span>';
                                    }
                                    ?>
                                </td>
                                <td><?php echo $user['last_login'] ? date('M d, Y H:i', strtotime($user['last_login'])) : 'Never'; ?></td>
                                <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                                <td>
                                    <div class="btn-group">
                                        <a href="<?php echo BASE_URL; ?>/?page=users&action=edit&id=<?php echo $user['id']; ?>" class="btn btn-sm btn-primary" title="Edit User">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                        <button type="button" class="btn btn-sm btn-danger" title="Delete User" data-bs-toggle="modal" data-bs-target="#deleteModal<?php echo $user['id']; ?>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <!-- Delete Modal -->
                                    <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                    <div class="modal fade" id="deleteModal<?php echo $user['id']; ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?php echo $user['id']; ?>" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="deleteModalLabel<?php echo $user['id']; ?>">Confirm Delete</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    Are you sure you want to delete the user "<?php echo htmlspecialchars($user['username']); ?>"?
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                    <form method="post" action="<?php echo BASE_URL; ?>/?page=users&action=delete&id=<?php echo $user['id']; ?>">
                                                        <button type="submit" class="btn btn-danger">Delete</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
        break;
}
?>
