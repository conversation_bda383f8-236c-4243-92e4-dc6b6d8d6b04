<?php
// Set page title
$pageTitle = 'Dashboard';

// Check if database update is needed
$needsUpdate = false;
try {
    // Try to access a column that should exist after the update
    $db->query("SELECT role FROM users LIMIT 1");
} catch (Exception $e) {
    $needsUpdate = true;
}

// Show update notification if needed
if ($needsUpdate) {
    echo '<div class="alert alert-warning alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>Database Update Required:</strong> Your database needs to be updated to use all the new features.
        <a href="' . BASE_URL . '/update.php" class="btn btn-sm btn-primary ms-2">Update Now</a>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>';
}

// Get statistics
$totalJobs = $db->getValue("SELECT COUNT(*) FROM jobs");
$totalPosts = $db->getValue("SELECT COUNT(*) FROM posts");
$totalImages = $db->getValue("SELECT COUNT(*) FROM images");
$recentJobs = $db->query("SELECT * FROM jobs ORDER BY created_at DESC LIMIT 5");
$recentPosts = $db->query("SELECT p.*, j.name as job_name FROM posts p JOIN jobs j ON p.job_id = j.id ORDER BY p.created_at DESC LIMIT 6");
?>

<div class="row">
    <div class="col-md-3">
        <div class="card mb-4 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="display-4 text-primary mb-2">
                    <i class="fas fa-tasks"></i>
                </div>
                <h5 class="card-title">Total Jobs</h5>
                <h2 class="mb-0"><?php echo $totalJobs; ?></h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card mb-4 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="display-4 text-success mb-2">
                    <i class="fas fa-newspaper"></i>
                </div>
                <h5 class="card-title">Total Posts</h5>
                <h2 class="mb-0"><?php echo $totalPosts; ?></h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card mb-4 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="display-4 text-info mb-2">
                    <i class="fas fa-images"></i>
                </div>
                <h5 class="card-title">Total Images</h5>
                <h2 class="mb-0"><?php echo $totalImages; ?></h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card mb-4 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="display-4 text-warning mb-2">
                    <i class="fas fa-clock"></i>
                </div>
                <h5 class="card-title">Last Grab</h5>
                <h6 class="mb-0">
                    <?php
                    $lastJob = $db->getRow("SELECT * FROM jobs WHERE last_run IS NOT NULL ORDER BY last_run DESC LIMIT 1");
                    echo $lastJob ? date('M d, H:i', strtotime($lastJob['last_run'])) : 'Never';
                    ?>
                </h6>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4 border-0 shadow-sm">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Jobs</h5>
                <a href="<?php echo BASE_URL; ?>/?page=jobs" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i> New Job
                </a>
            </div>
            <div class="card-body p-0">
                <?php if (empty($recentJobs)): ?>
                <div class="p-4 text-center">
                    <p class="text-muted mb-0">No jobs found. Create your first job to start grabbing content.</p>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Last Run</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recentJobs as $job): ?>
                            <tr>
                                <td>
                                    <a href="<?php echo BASE_URL; ?>/?page=jobs&action=view&id=<?php echo $job['id']; ?>">
                                        <?php echo htmlspecialchars($job['name']); ?>
                                    </a>
                                </td>
                                <td>
                                    <?php if ($job['type'] === 'wordpress'): ?>
                                    <span class="badge bg-primary">WordPress</span>
                                    <?php else: ?>
                                    <span class="badge bg-info">Sitemap</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    switch ($job['status']) {
                                        case 'pending':
                                            echo '<span class="badge bg-secondary">Pending</span>';
                                            break;
                                        case 'running':
                                            echo '<span class="badge bg-warning">Running</span>';
                                            break;
                                        case 'completed':
                                            echo '<span class="badge bg-success">Completed</span>';
                                            break;
                                        case 'failed':
                                            echo '<span class="badge bg-danger">Failed</span>';
                                            break;
                                    }
                                    ?>
                                </td>
                                <td>
                                    <?php echo $job['last_run'] ? date('M d, H:i', strtotime($job['last_run'])) : 'Never'; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card mb-4 border-0 shadow-sm">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Posts</h5>
                <a href="<?php echo BASE_URL; ?>/?page=posts" class="btn btn-sm btn-primary">
                    <i class="fas fa-list me-1"></i> All Posts
                </a>
            </div>
            <div class="card-body">
                <?php if (empty($recentPosts)): ?>
                <div class="text-center">
                    <p class="text-muted mb-0">No posts found. Run a job to grab content.</p>
                </div>
                <?php else: ?>
                <div class="row">
                    <?php foreach ($recentPosts as $post): ?>
                    <div class="col-md-6 mb-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <?php
                            $featuredImage = json_decode($post['featured_image'], true);
                            $imagePath = $featuredImage ? str_replace(BASE_PATH, BASE_URL, $featuredImage['local_path']) : BASE_URL . '/assets/images/placeholder.jpg';
                            ?>
                            <img src="<?php echo $imagePath; ?>" class="card-img-top" alt="<?php echo htmlspecialchars($post['title']); ?>" style="height: 150px; object-fit: cover;">
                            <div class="card-body">
                                <h6 class="card-title"><?php echo htmlspecialchars($post['title']); ?></h6>
                                <p class="card-text small text-muted">
                                    <i class="fas fa-calendar-alt me-1"></i> <?php echo date('M d, Y', strtotime($post['date_published'])); ?>
                                    <br>
                                    <i class="fas fa-tasks me-1"></i> <?php echo htmlspecialchars($post['job_name']); ?>
                                </p>
                                <a href="<?php echo BASE_URL; ?>/?page=posts&action=view&id=<?php echo $post['id']; ?>" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye me-1"></i> View
                                </a>
                                <a href="<?php echo BASE_URL; ?>/?page=process&id=<?php echo $post['id']; ?>" class="btn btn-sm btn-outline-success">
                                    <i class="fas fa-cogs me-1"></i> Process
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4 border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="<?php echo BASE_URL; ?>/?page=jobs&action=new" class="btn btn-primary d-block py-3">
                            <i class="fas fa-plus-circle fa-2x mb-2 d-block mx-auto"></i>
                            Create New Job
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?php echo BASE_URL; ?>/?page=jobs&action=run" class="btn btn-success d-block py-3">
                            <i class="fas fa-play-circle fa-2x mb-2 d-block mx-auto"></i>
                            Run All Jobs
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?php echo BASE_URL; ?>/?page=posts" class="btn btn-info d-block py-3">
                            <i class="fas fa-newspaper fa-2x mb-2 d-block mx-auto"></i>
                            Browse Posts
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?php echo BASE_URL; ?>/?page=settings" class="btn btn-secondary d-block py-3">
                            <i class="fas fa-cog fa-2x mb-2 d-block mx-auto"></i>
                            Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
