<?php
// Set page title
$pageTitle = 'Process Post';

// Get post ID
$postId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Get post data
$post = $db->getRow("SELECT p.*, j.name as job_name FROM posts p JOIN jobs j ON p.job_id = j.id WHERE p.id = ?", [$postId]);

if (!$post) {
    echo '<div class="alert alert-danger">Post not found!</div>';
    echo '<p><a href="' . BASE_URL . '/?page=posts" class="btn btn-primary">Back to Posts</a></p>';
    exit;
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'update_title':
            // Update post title
            $title = $_POST['title'] ?? '';
            if (!empty($title)) {
                $db->update('posts', ['title' => $title], 'id = ?', [$postId]);
                $post['title'] = $title;
                $successMessage = 'Title updated successfully!';
            } else {
                $errorMessage = 'Title cannot be empty!';
            }
            break;

        case 'update_content':
            // Update post content
            $content = $_POST['content'] ?? '';
            if (!empty($content)) {
                $db->update('posts', ['content' => $content], 'id = ?', [$postId]);
                $post['content'] = $content;
                $successMessage = 'Content updated successfully!';
            } else {
                $errorMessage = 'Content cannot be empty!';
            }
            break;

        case 'regenerate_html':
            // Regenerate HTML file
            $html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($post['title']) . '</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        img {
            max-width: 100%;
            height: auto;
        }
        .meta {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-bottom: 20px;
        }
        .categories, .tags {
            margin-top: 30px;
        }
        .categories span, .tags span {
            display: inline-block;
            background: #f1f1f1;
            padding: 3px 10px;
            border-radius: 3px;
            margin-right: 5px;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <h1>' . htmlspecialchars($post['title']) . '</h1>
    <div class="meta">Published on: ' . date('F j, Y', strtotime($post['date_published'])) . '</div>';

    // Add featured image if available
    $featuredImage = json_decode($post['featured_image'], true);
    if ($featuredImage) {
        $featuredImagePath = str_replace(BASE_PATH, BASE_URL, $featuredImage['local_path']);
        $html .= '<div class="featured-image">
        <img src="' . $featuredImagePath . '" alt="' . htmlspecialchars($featuredImage['alt'] ?? $post['title']) . '">
    </div>';
    }

    // Add content
    $html .= '<div class="content">' . $post['content'] . '</div>';

    // Get categories
    $categories = $db->query("
        SELECT c.*
        FROM categories c
        JOIN post_categories pc ON c.id = pc.category_id
        WHERE pc.post_id = ?
    ", [$post['id']]);

    // Add categories
    if (!empty($categories)) {
        $html .= '<div class="categories">Categories: ';
        foreach ($categories as $category) {
            $html .= '<span>' . htmlspecialchars($category['name']) . '</span> ';
        }
        $html .= '</div>';
    }

    // Get tags
    $tags = $db->query("
        SELECT t.*
        FROM tags t
        JOIN post_tags pt ON t.id = pt.tag_id
        WHERE pt.post_id = ?
    ", [$post['id']]);

    // Add tags
    if (!empty($tags)) {
        $html .= '<div class="tags">Tags: ';
        foreach ($tags as $tag) {
            $html .= '<span>' . htmlspecialchars($tag['name']) . '</span> ';
        }
        $html .= '</div>';
    }

    $html .= '</body>
</html>';

    // Create HTML directory if it doesn't exist
    if (!file_exists(HTML_DIR)) {
        mkdir(HTML_DIR, 0755, true);
    }

    // Save HTML file
    $htmlFile = HTML_DIR . '/' . $post['slug'] . '_' . $post['external_id'] . '.html';
    file_put_contents($htmlFile, $html);

    // Update post record
    $db->update('posts', ['html_file' => $htmlFile], 'id = ?', [$postId]);
    $post['html_file'] = $htmlFile;

    $successMessage = 'HTML file regenerated successfully!';
    break;

    case 'regenerate_pdf':
        // Regenerate PDF file using the PDF Generator
        require_once 'includes/PdfGenerator.php';

        // Create PDF directory if it doesn't exist
        if (!file_exists(PDF_DIR)) {
            mkdir(PDF_DIR, 0755, true);
        }

        // Get post categories
        $categories = $db->query("
            SELECT c.*
            FROM categories c
            JOIN post_categories pc ON c.id = pc.category_id
            WHERE pc.post_id = ?
        ", [$post['id']]);

        // Get post tags
        $tags = $db->query("
            SELECT t.*
            FROM tags t
            JOIN post_tags pt ON t.id = pt.tag_id
            WHERE pt.post_id = ?
        ", [$post['id']]);

        // Add categories and tags to post data
        $post['categories'] = $categories;
        $post['tags'] = $tags;

        // Generate PDF
        $pdfFile = PdfGenerator::generateFromPost($post);

        // Update post record
        $db->update('posts', ['pdf_file' => $pdfFile], 'id = ?', [$postId]);
        $post['pdf_file'] = $pdfFile;

        $successMessage = 'PDF file regenerated successfully!';
        break;
}
}

// Display success/error messages
if (isset($successMessage)) {
    echo '<div class="alert alert-success alert-dismissible fade show" role="alert">
        ' . htmlspecialchars($successMessage) . '
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>';
}

if (isset($errorMessage)) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
        ' . htmlspecialchars($errorMessage) . '
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>';
}

// Parse featured image
$featuredImage = json_decode($post['featured_image'], true);
?>

<div class="row">
    <div class="col-md-4">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="mb-0">Post Information</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Job:</strong> <?php echo htmlspecialchars($post['job_name']); ?>
                </div>
                <div class="mb-3">
                    <strong>URL:</strong> <a href="<?php echo $post['url']; ?>" target="_blank"><?php echo htmlspecialchars($post['url']); ?></a>
                </div>
                <div class="mb-3">
                    <strong>Date Published:</strong> <?php echo date('F j, Y', strtotime($post['date_published'])); ?>
                </div>
                <div class="mb-3">
                    <strong>Date Modified:</strong> <?php echo date('F j, Y', strtotime($post['date_modified'])); ?>
                </div>
                <div class="mb-3">
                    <strong>Images:</strong>
                    <?php
                    $imageCount = $db->getValue("SELECT COUNT(*) FROM images WHERE post_id = ?", [$post['id']]);
                    echo $imageCount;
                    ?>
                </div>
                <div class="mb-3">
                    <strong>Categories:</strong>
                    <?php
                    $categories = $db->query("
                        SELECT c.*
                        FROM categories c
                        JOIN post_categories pc ON c.id = pc.category_id
                        WHERE pc.post_id = ?
                    ", [$post['id']]);

                    if (empty($categories) || !is_array($categories)) {
                        echo 'None';
                    } else {
                        foreach ($categories as $index => $category) {
                            echo htmlspecialchars($category['name']);
                            if ($index < count($categories) - 1) {
                                echo ', ';
                            }
                        }
                    }
                    ?>
                </div>
                <div class="mb-3">
                    <strong>Tags:</strong>
                    <?php
                    $tags = $db->query("
                        SELECT t.*
                        FROM tags t
                        JOIN post_tags pt ON t.id = pt.tag_id
                        WHERE pt.post_id = ?
                    ", [$post['id']]);

                    if (empty($tags) || !is_array($tags)) {
                        echo 'None';
                    } else {
                        foreach ($tags as $index => $tag) {
                            echo htmlspecialchars($tag['name']);
                            if ($index < count($tags) - 1) {
                                echo ', ';
                            }
                        }
                    }
                    ?>
                </div>
            </div>
        </div>

        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="mb-0">Featured Image</h5>
            </div>
            <div class="card-body">
                <?php if ($featuredImage): ?>
                <img src="<?php echo str_replace(BASE_PATH, BASE_URL, $featuredImage['local_path']); ?>" alt="<?php echo htmlspecialchars($featuredImage['alt'] ?? $post['title']); ?>" class="img-fluid rounded mb-3">
                <div class="mb-3">
                    <strong>Alt Text:</strong> <?php echo htmlspecialchars($featuredImage['alt'] ?? ''); ?>
                </div>
                <?php else: ?>
                <div class="alert alert-info">No featured image available.</div>
                <?php endif; ?>
            </div>
        </div>

        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="mb-0">Generated Files</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>HTML File:</strong>
                    <?php if ($post['html_file'] && file_exists($post['html_file'])): ?>
                    <a href="<?php echo str_replace(BASE_PATH, BASE_URL, $post['html_file']); ?>" target="_blank" class="btn btn-sm btn-outline-primary mt-2">
                        <i class="fas fa-eye me-1"></i> View HTML
                    </a>
                    <form method="post" class="mt-2">
                        <input type="hidden" name="action" value="regenerate_html">
                        <button type="submit" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-sync me-1"></i> Regenerate HTML
                        </button>
                    </form>
                    <?php else: ?>
                    <div class="alert alert-warning mt-2">HTML file not found.</div>
                    <form method="post">
                        <input type="hidden" name="action" value="regenerate_html">
                        <button type="submit" class="btn btn-sm btn-primary">
                            <i class="fas fa-file-code me-1"></i> Generate HTML
                        </button>
                    </form>
                    <?php endif; ?>
                </div>

                <div class="mb-3">
                    <strong>PDF File:</strong>
                    <?php if ($post['pdf_file'] && file_exists($post['pdf_file'])): ?>
                    <a href="<?php echo str_replace(BASE_PATH, BASE_URL, $post['pdf_file']); ?>" target="_blank" class="btn btn-sm btn-outline-danger mt-2">
                        <i class="fas fa-eye me-1"></i> View PDF
                    </a>
                    <form method="post" class="mt-2">
                        <input type="hidden" name="action" value="regenerate_pdf">
                        <button type="submit" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-sync me-1"></i> Regenerate PDF
                        </button>
                    </form>
                    <?php else: ?>
                    <div class="alert alert-warning mt-2">PDF file not found.</div>
                    <form method="post">
                        <input type="hidden" name="action" value="regenerate_pdf">
                        <button type="submit" class="btn btn-sm btn-primary">
                            <i class="fas fa-file-pdf me-1"></i> Generate PDF
                        </button>
                    </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Title</h5>
                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editTitleModal">
                    <i class="fas fa-edit me-1"></i> Edit
                </button>
            </div>
            <div class="card-body">
                <h1><?php echo htmlspecialchars($post['title']); ?></h1>
            </div>
        </div>

        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Content</h5>
                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editContentModal">
                    <i class="fas fa-edit me-1"></i> Edit
                </button>
            </div>
            <div class="card-body">
                <div class="content">
                    <?php echo $post['content']; ?>
                </div>
            </div>
        </div>

        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="mb-0">Images</h5>
            </div>
            <div class="card-body">
                <?php
                $images = $db->query("SELECT * FROM images WHERE post_id = ?", [$post['id']]);

                if (empty($images)) {
                    echo '<div class="alert alert-info">No images found for this post.</div>';
                } else {
                    echo '<div class="row">';
                    foreach ($images as $image) {
                        echo '<div class="col-md-3 col-sm-4 col-6 mb-3">
                            <div class="card h-100">
                                <a href="' . str_replace(BASE_PATH, BASE_URL, $image['local_path']) . '" target="_blank">
                                    <img src="' . str_replace(BASE_PATH, BASE_URL, $image['local_path']) . '" alt="' . htmlspecialchars($image['alt']) . '" class="card-img-top">
                                </a>
                                <div class="card-body p-2">
                                    <p class="card-text small text-muted">' . htmlspecialchars($image['alt']) . '</p>
                                </div>
                            </div>
                        </div>';
                    }
                    echo '</div>';
                }
                ?>
            </div>
        </div>

        <div class="d-flex justify-content-between">
            <a href="<?php echo BASE_URL; ?>/?page=posts&action=view&id=<?php echo $post['id']; ?>" class="btn btn-secondary">
                <i class="fas fa-eye me-1"></i> View Post
            </a>
            <a href="<?php echo BASE_URL; ?>/?page=posts" class="btn btn-primary">
                <i class="fas fa-arrow-left me-1"></i> Back to Posts
            </a>
        </div>
    </div>
</div>

<!-- Edit Title Modal -->
<div class="modal fade" id="editTitleModal" tabindex="-1" aria-labelledby="editTitleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post">
                <input type="hidden" name="action" value="update_title">
                <div class="modal-header">
                    <h5 class="modal-title" id="editTitleModalLabel">Edit Title</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="title" class="form-label">Title</label>
                        <input type="text" class="form-control" id="title" name="title" value="<?php echo htmlspecialchars($post['title']); ?>" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Content Modal -->
<div class="modal fade" id="editContentModal" tabindex="-1" aria-labelledby="editContentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <form method="post">
                <input type="hidden" name="action" value="update_content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editContentModalLabel">Edit Content</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="content" class="form-label">Content</label>
                        <textarea class="form-control" id="content" name="content" rows="15" required><?php echo htmlspecialchars($post['content']); ?></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>
