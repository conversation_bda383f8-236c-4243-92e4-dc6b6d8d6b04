<?php
/**
 * Setup Wizard
 *
 * This file handles the setup process for the application.
 */

// Define base path
define('BASE_PATH', dirname(dirname(__FILE__)));

// Define setup steps
$steps = [
    1 => 'Welcome',
    2 => 'Database Setup',
    3 => 'User Setup',
    4 => 'Finish'
];

// Get current step
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
if ($step < 1 || $step > count($steps)) {
    $step = 1;
}

// Handle form submissions
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 2:
            // Database setup
            $dbHost = $_POST['db_host'] ?? '';
            $dbName = $_POST['db_name'] ?? '';
            $dbUser = $_POST['db_user'] ?? '';
            $dbPass = $_POST['db_pass'] ?? '';

            // Validate inputs
            if (empty($dbHost) || empty($dbName) || empty($dbUser)) {
                $error = 'All fields are required except password (if your database has no password).';
            } else {
                // Test database connection
                try {
                    $conn = new PDO("mysql:host=$dbHost", $dbUser, $dbPass);
                    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                    // Create database if it doesn't exist
                    $conn->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

                    // Select the database
                    $conn->exec("USE `$dbName`");

                    // Create tables
                    $tables = [
                        "CREATE TABLE IF NOT EXISTS `jobs` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `name` varchar(255) NOT NULL,
                            `url` varchar(255) NOT NULL,
                            `category_id` int(11) DEFAULT NULL,
                            `type` enum('wordpress','sitemap') NOT NULL DEFAULT 'wordpress',
                            `category` varchar(255) DEFAULT NULL,
                            `disable_embed` tinyint(1) NOT NULL DEFAULT 0,
                            `posts_per_run` int(11) NOT NULL DEFAULT 10,
                            `after_date` date DEFAULT NULL,
                            `before_date` date DEFAULT NULL,
                            `status` enum('pending','running','completed','failed') NOT NULL DEFAULT 'pending',
                            `schedule_type` varchar(50) DEFAULT NULL,
                            `frequency` varchar(50) DEFAULT 'daily',
                            `last_run` datetime DEFAULT NULL,
                            `last_run_posts` int(11) DEFAULT 0,
                            `error` text DEFAULT NULL,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            KEY `category_id` (`category_id`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `posts` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `job_id` int(11) NOT NULL,
                            `external_id` varchar(255) NOT NULL,
                            `title` varchar(255) NOT NULL,
                            `content` longtext NOT NULL,
                            `excerpt` text DEFAULT NULL,
                            `url` varchar(255) NOT NULL,
                            `slug` varchar(255) NOT NULL,
                            `date_published` datetime NOT NULL,
                            `date_modified` datetime NOT NULL,
                            `featured_image` text DEFAULT NULL,
                            `html_file` varchar(255) DEFAULT NULL,
                            `pdf_file` varchar(255) DEFAULT NULL,
                            `metadata` text DEFAULT NULL,
                            `processed` tinyint(1) NOT NULL DEFAULT 0,
                            `is_future` tinyint(1) NOT NULL DEFAULT 0,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            UNIQUE KEY `external_id` (`external_id`),
                            UNIQUE KEY `url` (`url`),
                            KEY `job_id` (`job_id`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `categories` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `name` varchar(255) NOT NULL,
                            `slug` varchar(255) NOT NULL,
                            `created_at` datetime NOT NULL,
                            PRIMARY KEY (`id`),
                            UNIQUE KEY `name` (`name`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `tags` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `name` varchar(255) NOT NULL,
                            `slug` varchar(255) NOT NULL,
                            `created_at` datetime NOT NULL,
                            PRIMARY KEY (`id`),
                            UNIQUE KEY `name` (`name`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `post_categories` (
                            `post_id` int(11) NOT NULL,
                            `category_id` int(11) NOT NULL,
                            PRIMARY KEY (`post_id`,`category_id`),
                            KEY `category_id` (`category_id`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `post_tags` (
                            `post_id` int(11) NOT NULL,
                            `tag_id` int(11) NOT NULL,
                            PRIMARY KEY (`post_id`,`tag_id`),
                            KEY `tag_id` (`tag_id`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `images` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `post_id` int(11) NOT NULL,
                            `url` varchar(255) NOT NULL,
                            `local_path` varchar(255) NOT NULL,
                            `optimized_path` varchar(255) DEFAULT NULL,
                            `alt` varchar(255) DEFAULT NULL,
                            `caption` text DEFAULT NULL,
                            `is_featured` tinyint(1) NOT NULL DEFAULT 0,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            KEY `post_id` (`post_id`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `users` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `username` varchar(255) NOT NULL,
                            `password` varchar(255) NOT NULL,
                            `email` varchar(255) NOT NULL,
                            `role` enum('admin','editor','viewer') NOT NULL DEFAULT 'viewer',
                            `api_key` varchar(255) DEFAULT NULL,
                            `remember_token` varchar(255) DEFAULT NULL,
                            `token_expires` datetime DEFAULT NULL,
                            `last_login` datetime DEFAULT NULL,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            UNIQUE KEY `username` (`username`),
                            UNIQUE KEY `email` (`email`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `job_runs` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `job_id` int(11) NOT NULL,
                            `status` enum('pending','running','completed','failed') NOT NULL DEFAULT 'pending',
                            `start_time` datetime NOT NULL,
                            `end_time` datetime DEFAULT NULL,
                            `posts_grabbed` int(11) NOT NULL DEFAULT 0,
                            `error` text DEFAULT NULL,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            KEY `job_id` (`job_id`),
                            KEY `status` (`status`),
                            KEY `start_time` (`start_time`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `job_stats` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `job_id` int(11) NOT NULL,
                            `run_date` datetime NOT NULL,
                            `total_posts` int(11) NOT NULL DEFAULT 0,
                            `new_posts` int(11) NOT NULL DEFAULT 0,
                            `updated_posts` int(11) NOT NULL DEFAULT 0,
                            `skipped_posts` int(11) NOT NULL DEFAULT 0,
                            `execution_time` float NOT NULL DEFAULT 0,
                            `approaches_tried` int(11) NOT NULL DEFAULT 0,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            KEY `job_id` (`job_id`),
                            KEY `run_date` (`run_date`)
                        )",
                        "CREATE TABLE IF NOT EXISTS `settings` (
                            `id` int(11) NOT NULL AUTO_INCREMENT,
                            `key` varchar(255) NOT NULL,
                            `value` text DEFAULT NULL,
                            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            PRIMARY KEY (`id`),
                            UNIQUE KEY `key` (`key`)
                        )"
                    ];

                    foreach ($tables as $table) {
                        $conn->exec($table);
                    }

                    // Create config file
                    $configContent = file_get_contents(BASE_PATH . '/config.php');
                    $configContent = str_replace("define('DB_HOST', '');", "define('DB_HOST', '$dbHost');", $configContent);
                    $configContent = str_replace("define('DB_NAME', '');", "define('DB_NAME', '$dbName');", $configContent);
                    $configContent = str_replace("define('DB_USER', '');", "define('DB_USER', '$dbUser');", $configContent);
                    $configContent = str_replace("define('DB_PASS', '');", "define('DB_PASS', '$dbPass');", $configContent);

                    file_put_contents(BASE_PATH . '/config.php', $configContent);

                    $success = 'Database setup completed successfully!';

                    // Redirect to next step
                    header('Location: index.php?step=3');
                    exit;
                } catch (PDOException $e) {
                    $error = 'Database connection failed: ' . $e->getMessage();
                }
            }
            break;

        case 3:
            // User setup
            $username = $_POST['username'] ?? '';
            $password = $_POST['password'] ?? '';
            $email = $_POST['email'] ?? '';

            // Validate inputs
            if (empty($username) || empty($password) || empty($email)) {
                $error = 'All fields are required.';
            } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $error = 'Invalid email address.';
            } else {
                // Load config
                require_once BASE_PATH . '/config.php';

                try {
                    // Connect to database
                    $conn = get_db_connection();

                    // Hash password
                    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

                    // Insert user
                    $stmt = $conn->prepare("INSERT INTO users (username, password, email, created_at) VALUES (?, ?, ?, ?)");
                    $stmt->execute([$username, $hashedPassword, $email, date('Y-m-d H:i:s')]);

                    $success = 'User created successfully!';

                    // Redirect to next step
                    header('Location: index.php?step=4');
                    exit;
                } catch (PDOException $e) {
                    $error = 'Error creating user: ' . $e->getMessage();
                }
            }
            break;
    }
}

// Page title
$pageTitle = 'Setup - ' . $steps[$step];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 40px;
        }
        .setup-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .setup-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .setup-header h1 {
            color: #3498db;
            font-weight: 700;
        }
        .setup-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            position: relative;
        }
        .setup-steps::before {
            content: '';
            position: absolute;
            top: 15px;
            left: 0;
            right: 0;
            height: 2px;
            background-color: #e9ecef;
            z-index: 1;
        }
        .step {
            position: relative;
            z-index: 2;
            background-color: #fff;
            width: 60px;
            text-align: center;
        }
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            line-height: 30px;
            border-radius: 50%;
            background-color: #e9ecef;
            color: #6c757d;
            font-weight: 600;
            margin-bottom: 5px;
        }
        .step.active .step-number {
            background-color: #3498db;
            color: #fff;
        }
        .step-label {
            font-size: 12px;
            color: #6c757d;
        }
        .step.active .step-label {
            color: #3498db;
            font-weight: 600;
        }
        .setup-content {
            margin-bottom: 30px;
        }
        .setup-footer {
            display: flex;
            justify-content: space-between;
        }
        .btn-primary {
            background-color: #3498db;
            border-color: #3498db;
        }
        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="setup-container">
            <div class="setup-header">
                <h1>Content Grabber Setup</h1>
                <p class="text-muted">Setting up your content grabbing system</p>
            </div>

            <div class="setup-steps">
                <?php foreach ($steps as $stepNumber => $stepName): ?>
                <div class="step <?php echo $stepNumber == $step ? 'active' : ''; ?>">
                    <div class="step-number"><?php echo $stepNumber; ?></div>
                    <div class="step-label"><?php echo $stepName; ?></div>
                </div>
                <?php endforeach; ?>
            </div>

            <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if ($success): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <div class="setup-content">
                <?php
                // Include step content
                switch ($step) {
                    case 1:
                        include 'welcome.php';
                        break;
                    case 2:
                        include 'database.php';
                        break;
                    case 3:
                        include 'user.php';
                        break;
                    case 4:
                        include 'finish.php';
                        break;
                }
                ?>
            </div>

            <div class="setup-footer">
                <?php if ($step > 1): ?>
                <a href="?step=<?php echo $step - 1; ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Previous
                </a>
                <?php else: ?>
                <div></div>
                <?php endif; ?>

                <?php if ($step < count($steps)): ?>
                <?php if ($step == 1): ?>
                <a href="?step=<?php echo $step + 1; ?>" class="btn btn-primary">
                    Next <i class="fas fa-arrow-right"></i>
                </a>
                <?php endif; ?>
                <?php elseif ($step == count($steps)): ?>
                <a href="../index.php" class="btn btn-success">
                    <i class="fas fa-check"></i> Finish Setup
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
