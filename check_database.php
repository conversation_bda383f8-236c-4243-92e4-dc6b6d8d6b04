<?php
/**
 * Database Check Script
 * 
 * This script checks the database structure and connection.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load configuration
require_once 'config.php';

// Load required files
require_once 'includes/Database.php';

echo "<h1>Database Check</h1>";

try {
    // Initialize database connection
    echo "<p>Initializing database connection...</p>";
    $db = new Database();
    echo "<p>✓ Database connection successful</p>";

    // Check if jobs table exists
    echo "<p>Checking if jobs table exists...</p>";
    $tables = $db->query("SHOW TABLES LIKE 'jobs'");
    
    if (empty($tables)) {
        echo "<p style='color: red;'>✗ Jobs table does not exist</p>";
        
        // Try to create the jobs table
        echo "<p>Attempting to create jobs table...</p>";
        $createTableSQL = "
            CREATE TABLE jobs (
                id INT(11) NOT NULL AUTO_INCREMENT,
                name VARCHAR(255) NOT NULL,
                url VARCHAR(255) NOT NULL,
                category_id INT(11) DEFAULT NULL,
                type ENUM('wordpress', 'sitemap') NOT NULL DEFAULT 'wordpress',
                category VARCHAR(255) DEFAULT NULL,
                disable_embed TINYINT(1) NOT NULL DEFAULT 0,
                posts_per_run INT(11) NOT NULL DEFAULT 10,
                after_date DATE DEFAULT NULL,
                before_date DATE DEFAULT NULL,
                status ENUM('pending', 'running', 'completed', 'failed') NOT NULL DEFAULT 'pending',
                schedule_type VARCHAR(50) DEFAULT NULL,
                frequency VARCHAR(50) DEFAULT 'daily',
                last_run DATETIME DEFAULT NULL,
                last_run_posts INT(11) DEFAULT 0,
                error TEXT DEFAULT NULL,
                created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY category_id (category_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $db->query($createTableSQL);
        echo "<p>✓ Jobs table created successfully</p>";
    } else {
        echo "<p>✓ Jobs table exists</p>";
    }

    // Show table structure
    echo "<p>Jobs table structure:</p>";
    $columns = $db->query("SHOW COLUMNS FROM jobs");
    
    if (!empty($columns)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    // Check other important tables
    $importantTables = ['posts', 'categories', 'tags', 'images', 'users'];
    
    echo "<p>Checking other important tables:</p>";
    foreach ($importantTables as $tableName) {
        $tableExists = $db->query("SHOW TABLES LIKE '$tableName'");
        if (empty($tableExists)) {
            echo "<p style='color: orange;'>⚠ Table '$tableName' does not exist</p>";
        } else {
            echo "<p>✓ Table '$tableName' exists</p>";
        }
    }

    // Test a simple insert and delete
    echo "<p>Testing database operations...</p>";
    
    // Test insert
    $testData = [
        'name' => 'Test Job for DB Check',
        'url' => 'https://test.example.com',
        'type' => 'wordpress',
        'posts_per_run' => 5,
        'status' => 'pending',
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    $insertId = $db->insert('jobs', $testData);
    echo "<p>✓ Test insert successful, ID: $insertId</p>";
    
    // Test select
    $testJob = $db->getRow("SELECT * FROM jobs WHERE id = ?", [$insertId]);
    if ($testJob) {
        echo "<p>✓ Test select successful</p>";
    } else {
        echo "<p style='color: red;'>✗ Test select failed</p>";
    }
    
    // Test delete
    $db->query("DELETE FROM jobs WHERE id = ?", [$insertId]);
    echo "<p>✓ Test delete successful</p>";

} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<p><a href='" . BASE_URL . "/?page=jobs'>Back to Jobs</a></p>";
?>
